root@iZ7xvczjzdx1cgmypn8ufmZ:~# cd /var/www/customer_system
root@iZ7xvczjzdx1cgmypn8ufmZ:/var/www/customer_system# chmod +x restart_backend.sh monitor_memory.sh
root@iZ7xvczjzdx1cgmypn8ufmZ:/var/www/customer_system# ./restart_backend.sh
=== 后端服务重启脚本 ===
时间: Mon Jul 28 10:41:32 AM CST 2025

1. 停止当前后端服务...
[PM2] Applying action stopProcessId on app [customer-backend](ids: [ 1 ])
[PM2] [customer-backend](1) ✓
┌────┬─────────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name                │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼─────────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 1  │ customer-backend    │ default     │ 1.0.0   │ fork    │ 0        │ 0      │ 1163 │ stopped   │ 0%       │ 0b       │ root     │ disabled │
│ 3  │ partner-admin       │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
│ 2  │ partner-server      │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
└────┴─────────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
2. 删除旧的进程...
[PM2] Applying action deleteProcessId on app [customer-backend](ids: [ 1 ])
[PM2] [customer-backend](1) ✓
┌────┬───────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name              │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 3  │ partner-admin     │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
│ 2  │ partner-server    │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
└────┴───────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
3. 使用新配置启动服务...
[PM2][WARN] Applications customer-backend not running, starting...
[PM2][WARN] Folder does not exist: /var/www/customer_system/backend/logs
[PM2] Creating folder: /var/www/customer_system/backend/logs
[PM2] App [customer-backend] launched (1 instances)
┌────┬─────────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name                │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼─────────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 4  │ customer-backend    │ default     │ 1.0.0   │ fork    │ 605299   │ 0s     │ 0    │ online    │ 0%       │ 18.9mb   │ root     │ disabled │
│ 3  │ partner-admin       │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
│ 2  │ partner-server      │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
└────┴─────────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
4. 检查服务状态...
┌────┬─────────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name                │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼─────────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 4  │ customer-backend    │ default     │ 1.0.0   │ fork    │ 605299   │ 3s     │ 0    │ online    │ 0%       │ 89.0mb   │ root     │ disabled │
│ 3  │ partner-admin       │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
│ 2  │ partner-server      │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
└────┴─────────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘

5. 检查内存使用情况...
 Describing process with id 4 - name customer-backend 
┌───────────────────┬──────────────────────────────────────────────────────────────────┐
│ status            │ online                                                           │
│ name              │ customer-backend                                                 │
│ namespace         │ default                                                          │
│ version           │ 1.0.0                                                            │
│ restarts          │ 0                                                                │
│ uptime            │ 3s                                                               │
│ entire log path   │ /var/www/customer_system/backend/logs/customer-backend.log       │
│ script path       │ /var/www/customer_system/backend/index.js                        │
│ script args       │ N/A                                                              │
│ error log path    │ /var/www/customer_system/backend/logs/customer-backend-error.log │
│ out log path      │ /var/www/customer_system/backend/logs/customer-backend-out.log   │
│ pid path          │ /root/.pm2/pids/customer-backend-4.pid                           │
│ interpreter       │ /usr/bin/node                                                    │
│ interpreter args  │ N/A                                                              │
│ script id         │ 4                                                                │
│ exec cwd          │ /var/www/customer_system/backend                                 │
│ exec mode         │ fork_mode                                                        │
│ node.js version   │ 20.19.0                                                          │
│ node env          │ production                                                       │
│ watch & reload    │ ✘                                                                │
│ unstable restarts │ 0                                                                │
│ created at        │ 2025-07-28T02:41:36.267Z                                         │
└───────────────────┴──────────────────────────────────────────────────────────────────┘
 Actions available 
┌────────────────────────┐
│ km:heapdump            │
│ km:cpu:profiling:start │
│ km:cpu:profiling:stop  │
│ km:heap:sampling:start │
│ km:heap:sampling:stop  │
└────────────────────────┘
 Trigger via: pm2 trigger customer-backend <action_name>

 Code metrics value 
┌────────────────────────┬───────────┐
│ Used Heap Size         │ 19.24 MiB │
│ Heap Usage             │ 35.58 %   │
│ Heap Size              │ 54.07 MiB │
│ Event Loop Latency p95 │ 0.42 ms   │
│ Event Loop Latency     │ 0.04 ms   │
│ Active handles         │ 5         │
│ Active requests        │ 0         │
└────────────────────────┴───────────┘
 Divergent env variables from local env 
┌─────┬───────────┐
│ PWD │ ./backend │
└─────┴───────────┘

 Add your own code metrics: http://bit.ly/code-metrics
 Use `pm2 logs customer-backend [--lines 1000]` to display logs
 Use `pm2 env 4` to display environment variables
 Use `pm2 monit` to monitor CPU and Memory usage customer-backend

6. 查看最新日志...
[TAILING] Tailing last 20 lines for [customer-backend] process (change the value with --lines option)
/var/www/customer_system/backend/logs/customer-backend-error.log last 20 lines:
4|customer | 2025-07-28 10:41:36 +08:00: Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
4|customer | 2025-07-28 10:41:36 +08:00: Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
4|customer | 2025-07-28 10:41:36 +08:00: Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
4|customer | 2025-07-28 10:41:36 +08:00: Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
4|customer | 2025-07-28 10:41:36 +08:00: Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
4|customer | 2025-07-28 10:41:36 +08:00: Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection

/var/www/customer_system/backend/logs/customer-backend-out.log last 20 lines:
4|customer | 2025-07-28 10:41:36 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-28 10:41:36 +08:00: 服务器正在端口 3002 上运行.


root@iZ7xvczjzdx1cgmypn8ufmZ:~# ./monitor_memory.sh
-bash: ./monitor_memory.sh: No such file or directory
root@iZ7xvczjzdx1cgmypn8ufmZ:~# cd /var/www/customer_system
root@iZ7xvczjzdx1cgmypn8ufmZ:/var/www/customer_system# ./monitor_memory.sh
=== 后端内存监控脚本 ===
时间: Mon Jul 28 10:42:22 AM CST 2025

1. PM2进程状态:
┌────┬─────────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name                │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼─────────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 4  │ customer-backend    │ default     │ 1.0.0   │ fork    │ 605299   │ 46s    │ 0    │ online    │ 0%       │ 70.9mb   │ root     │ disabled │
│ 3  │ partner-admin       │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
│ 2  │ partner-server      │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
└────┴─────────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘

2. 详细内存信息:
│ restarts          │ 0                                                                │
│ uptime            │ 46s                                                              │
│ unstable restarts │ 0                                                                │
│ km:heapdump            │
│ km:heap:sampling:start │
│ km:heap:sampling:stop  │

3. 系统内存使用:
               total        used        free      shared  buff/cache   available
Mem:           3.5Gi       1.2Gi       752Mi       3.4Mi       1.8Gi       2.3Gi
Swap:             0B          0B          0B

4. Node.js进程内存:
root      605299  1.9  1.9 1106168 72620 ?       Ssl  10:41   0:00 node /var/www/customer_system/backend/index.js

5. 最近的错误日志:
--- 错误日志 (最近10行) ---
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection

6. 最近的输出日志:
--- 输出日志 (最近10行) ---
数据库连接成功，且模型关联已加载。
服务器正在端口 3002 上运行.
Executing (default): SELECT 1+1 AS result
数据库连接成功，且模型关联已加载。
服务器正在端口 3002 上运行.
Executing (default): SELECT 1+1 AS result
数据库连接成功，且模型关联已加载。
服务器正在端口 3002 上运行.
收到 SIGINT 信号，开始优雅关闭...
数据库连接已关闭

7. 数据库连接测试:
测试数据库连接...
后端服务已成功启动！ - 后端服务响应正常

=== 监控完成 ===
如果重启次数持续增加，说明仍有内存泄漏问题
正常情况下，内存使用应该稳定在512M以下
root@iZ7xvczjzdx1cgmypn8ufmZ:/var/www/customer_system# 