server {
    server_name service.bogoo.net;

    # 增加客户端最大请求体大小，支持文件上传
    client_max_body_size 100M;

    # 小程序API接口 - 后端运行在3002端口
    location /api/ {
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' 'https://admin.bogoo.net' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain charset=UTF-8' always;
            add_header 'Content-Length' 0 always;
            return 204;
        }

        # 修复：正确的代理路径，不重复/api/
        proxy_pass http://127.0.0.1:3002;

        # 基本头部设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 关闭缓冲，支持POST请求
        proxy_buffering off;
        proxy_request_buffering off;

        # 添加CORS头部到所有响应
        add_header 'Access-Control-Allow-Origin' 'https://admin.bogoo.net' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;
    }

    # 静态文件（如上传的图片）
    location /uploads/ {
        # 修复：正确的代理路径，不重复/uploads/
        proxy_pass http://127.0.0.1:3002;

        # 基本头部设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 关闭缓冲
        proxy_buffering off;
        proxy_request_buffering off;
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/service.bogoo.net/fullchain.pem; # managed by Certbot
    ssl_certif