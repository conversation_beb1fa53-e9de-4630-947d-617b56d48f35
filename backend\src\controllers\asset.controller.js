const db = require('../models');
const { Op } = require('sequelize'); // 引入Op，用于复杂查询
const { generateAssetId, generateAssetChangeId } = require('../utils/id_helper'); // 引入新函数

const Asset = db.Asset;
const AssetChangeLog = db.AssetChangeLog;
const User = db.User;
const Enterprise = db.Enterprise;
const Product = db.Product;
const ProductFeature = db.ProductFeature;
const Employee = db.Employee;
const OrderHead = db.OrderHead;

/**
 * [新增] 获取下一个可用的资产ID
 */
exports.getNextAssetId = async (req, res) => {
  try {
    const nextId = await generateAssetId();
    res.status(200).json({ next_id: nextId });
  } catch (error) {
    console.error('获取下一个资产ID时出错:', error);
    res.status(500).json({ message: '生成资产ID失败', error: error.message });
  }
};

/**
 * [新增] 获取下一个可用的资产变更ID
 */
exports.getNextAssetChangeId = async (req, res) => {
    try {
        const nextId = await generateAssetChangeId();
        res.status(200).json({ next_id: nextId });
    } catch (error) {
        console.error('获取下一个资产变更ID时出错:', error);
        res.status(500).json({ message: '生成资产变更ID失败', error: error.message });
    }
};

// 创建新资产
exports.createAsset = async (req, res) => {
  try {
    // [重构] 统一从 asset_data 中获取数据
    const assetData = req.body.asset_data;

    if (!assetData) {
        return res.status(400).json({ message: '创建资产时必须提供 asset_data 字段。' });
    }

    if (!assetData.asset_id) {
        return res.status(400).json({ message: '创建资产时必须提供资产ID (asset_id)。' });
    }

    // [新增] 验证必填字段
    const requiredFields = ['enterprise_id', 'product_id', 'user_count', 'account_count'];
    const missingFields = requiredFields.filter(field => !assetData[field] && assetData[field] !== 0);
    if (missingFields.length > 0) {
        return res.status(400).json({
            message: `创建资产时缺少必填字段: ${missingFields.join(', ')}`,
            missingFields: missingFields
        });
    }

    // [重构] 严格进行唯一性校验
    const existingAsset = await Asset.findOne({ where: { asset_id: assetData.asset_id } });
    if (existingAsset) {
      return res.status(409).json({ message: `资产ID '${assetData.asset_id}' 已存在。` });
    }

    // [新增] 添加创建人信息
    const finalAssetData = {
      ...assetData,
      creator_id: req.user?.id // 从JWT token中获取当前用户ID
    };

    const newAsset = await Asset.create(finalAssetData);

    res.status(201).json(newAsset);
  } catch (error) {
    console.error("创建资产失败:", error);
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: '数据验证失败',
        errors: error.errors.map(e => ({
          field: e.path,
          message: e.message,
          value: e.value
        }))
      });
    }
    res.status(500).json({ message: '创建资产失败', error: error.message });
  }
};

// 获取所有资产
exports.getAllAssets = async (req, res) => {
    try {
        const { q, enterpriseId } = req.query;
        let where = {};

        // 权限控制：根据用户类型过滤资产
        let enterpriseWhere = {};
        if (req.user) {
            if (req.user.type === 'user') {
                // 普通用户只能看到自己绑定企业的资产
                enterpriseWhere.user_id = req.user.id;
            } else if (req.user.type === 'employee' && req.user.role !== 'admin') {
                // 非管理员员工只能看到自己负责企业的资产
                enterpriseWhere.employee_id = req.user.id;
            }
            // 管理员可以看到所有企业的资产，不添加额外过滤条件
        }

        if (q) {
            where[Op.or] = [
                { asset_id: { [Op.like]: `%${q}%` } },
                { '$product.product_name$': { [Op.like]: `%${q}%` } }
            ];
        }
        if (enterpriseId) {
            where.enterprise_id = enterpriseId;
        }

        const assets = await Asset.findAll({
            where,
            include: [
                {
                    model: Enterprise,
                    as: 'enterprise',
                    where: enterpriseWhere // 添加企业权限过滤
                },
                { model: Product, as: 'product' },
                { model: User, as: 'user' },
                { model: Employee, as: 'creator' } //  [修复] 新增对创建人(Employee)的关联查询
            ]
        });
        res.status(200).json(assets);
    } catch (error) {
        res.status(500).json({ message: "获取资产列表失败", error: error.message });
    }
};

// 获取单个资产详情，包括变更记录和关联订单
exports.getAssetById = async (req, res) => {
    try {
        const { id } = req.params;
        const asset = await Asset.findByPk(id, {
            include: [
                { model: User, as: 'user' },
                { model: Enterprise, as: 'enterprise' },
                {
                    model: Product,
                    as: 'product',
                    include: [
                        {
                            model: ProductFeature,
                            as: 'features',
                            attributes: ['id', 'feature_id', 'feature_name', 'description'],
                            through: {
                                attributes: ['id', 'feature_price', 'remark']
                            }
                        }
                    ]
                },
                { model: Employee, as: 'creator' }, // [修复] 新增对创建人(Employee)的关联查询
                {
                    model: AssetChangeLog,
                    as: 'changeLogs',
                    include: [{ model: Employee, as: 'creator' }]
                },
                // [新增] 添加关联订单查询
                {
                    model: OrderHead,
                    as: 'orders', // 使用在模型中定义的别名
                    include: [
                        { model: Employee, as: 'creator', attributes: ['id', 'name'] }
                    ],
                    attributes: ['id', 'order_id', 'order_category', 'order_type', 'actual_amount', 'payment_status', 'audit_status', 'createdAt', 'remark'],
                    order: [['createdAt', 'DESC']]
                }
            ],
            order: [[{ model: AssetChangeLog, as: 'changeLogs' }, 'change_date', 'DESC']] // [修复] 排序字段改为 change_date
        });
        if (asset) {
            res.status(200).json(asset);
        } else {
            res.status(404).json({ message: '未找到指定资产' });
        }
    } catch (error) {
        console.error('获取资产详情时出错:', error);
        res.status(500).json({ message: '获取资产详情失败', error: error.message });
    }
};

// "修改"资产 (普通更新)
exports.updateAsset = async (req, res) => {
  try {
    const { id } = req.params;
    const assetData = req.body;

    // [新增] 验证 asset_id 唯一性
    if (assetData.asset_id) {
      const existing = await Asset.findOne({ where: { asset_id: assetData.asset_id, id: { [Op.ne]: id } } });
      if (existing) {
        return res.status(409).json({ message: `资产ID '${assetData.asset_id}' 已被其他资产占用。` });
      }
    }

    const asset = await Asset.findByPk(id);
    if (!asset) {
      return res.status(404).json({ message: '未找到指定资产' });
    }
    await asset.update(assetData);
    res.status(200).json(asset);
  } catch (error) {
    console.error('更新资产时出错:', error);
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({ message: '数据验证失败', errors: error.errors.map(e => e.message) });
    }
    res.status(500).json({ message: '更新资产失败', error: error.message });
  }
};

// "回滚"资产变更
exports.revertAssetChange = async (req, res) => {
    const { changeId } = req.params; // 注意：这里我们用changeId来操作
    const t = await db.sequelize.transaction();
    try {
        const log = await AssetChangeLog.findByPk(changeId, { transaction: t });
        if (!log) {
            await t.rollback();
            return res.status(404).json({ message: '未找到该变更记录' });
        }

        // 1. 从快照中恢复数据
        const snapshotBefore = log.snapshot_before;
        const asset = await Asset.findByPk(log.asset_id, { transaction: t });
        if (!asset) {
            await t.rollback();
            return res.status(404).json({ message: '未找到与记录关联的资产' });
        }
        await asset.update(snapshotBefore, { transaction: t });

        // 2. 删除此条变更记录
        await log.destroy({ transaction: t });

        await t.commit();
        res.status(200).json({ message: '变更已成功回滚', asset });

    } catch (error) {
        await t.rollback();
        console.error('回滚资产变更时出错:', error);
        res.status(500).json({ message: '回滚失败', error: error.message });
    }
};




/**
 * [新增] 根据资产ID获取其所有变更记录
 */
exports.getChangesByAssetId = async (req, res) => {
  try {
    const { assetId } = req.params;
    const { page = 1, pageSize = 10 } = req.query;

    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    const { count, rows } = await AssetChangeLog.findAndCountAll({
      where: { asset_id: assetId },
      include: [{ model: Employee, as: 'creator', attributes: ['id', 'name'] }],
      order: [['change_date', 'DESC'], ['createdAt', 'DESC']],
      offset,
      limit
    });

    res.status(200).json({
      records: rows,
      total: count,
      page: parseInt(page),
      pageSize: limit
    });
  } catch (error) {
    console.error('获取资产变更记录时出错:', error);
    res.status(500).json({ message: '获取资产变更记录失败', error: error.message });
  }
};

/**
 * [新增] 创建资产变更记录 (新的API接口)
 */
exports.createAssetChange = async (req, res) => {
    try {
        const changeData = req.body;

        // 验证必填字段
        if (!changeData.asset_change_id) {
            return res.status(400).json({ message: '必须提供资产变更ID (asset_change_id)。' });
        }

        if (!changeData.asset_id) {
            return res.status(400).json({ message: '必须提供资产ID (asset_id)。' });
        }

        // 验证变更ID唯一性
        const existingChange = await AssetChangeLog.findOne({
            where: { asset_change_id: changeData.asset_change_id }
        });
        if (existingChange) {
            return res.status(409).json({
                message: `资产变更ID '${changeData.asset_change_id}' 已存在。`
            });
        }

        // 验证资产是否存在
        const asset = await Asset.findByPk(changeData.asset_id);
        if (!asset) {
            return res.status(404).json({ message: '未找到指定资产' });
        }

        const t = await db.sequelize.transaction();
        try {
            // 更新资产数据
            if (changeData.snapshot_after) {
                await asset.update(changeData.snapshot_after, { transaction: t });
            }

            // 创建变更记录
            const newChange = await AssetChangeLog.create({
                asset_change_id: changeData.asset_change_id,
                change_date: changeData.change_date || new Date(),
                asset_id: parseInt(changeData.asset_id, 10),
                snapshot_before: changeData.snapshot_before || {},
                snapshot_after: changeData.snapshot_after || {},
                remark: changeData.remark || '',
                creator_id: changeData.creator?.id || req.user?.id, // 从请求数据或JWT token获取
            }, { transaction: t });

            await t.commit();
            res.status(201).json({
                message: '资产变更记录创建成功',
                change: newChange
            });

        } catch (error) {
            await t.rollback();
            throw error;
        }

    } catch (error) {
        console.error('创建资产变更记录时出错:', error);
        if (error.name === 'SequelizeValidationError') {
            return res.status(400).json({
                message: '数据验证失败',
                errors: error.errors.map(e => e.message)
            });
        }
        res.status(500).json({
            message: '创建资产变更记录失败',
            error: error.message
        });
    }
};

/**
 * [新增] 获取所有资产变更记录列表
 */
exports.getAllAssetChanges = async (req, res) => {
  try {
    const changes = await AssetChangeLog.findAll({
      include: [
        {
          model: Employee,
          as: 'creator',
          attributes: ['id', 'name']
        },
        {
          model: Asset,
          as: 'asset',
          attributes: ['id', 'asset_id'],
          include: [
            {
              model: Enterprise,
              as: 'enterprise',
              attributes: ['id', 'name']
            },
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'product_name', 'version_name']
            }
          ]
        }
      ],
      order: [['change_date', 'DESC']] // 按变更时间降序排序
    });
    res.status(200).json(changes);
  } catch (error) {
    console.error('获取所有资产变更记录时出错:', error);
    res.status(500).json({ message: '获取所有资产变更记录失败', error: error.message });
  }
};

/**
 * [新增] 根据变更记录ID获取单个变更记录详情
 */
exports.getAssetChangeById = async (req, res) => {
  try {
    const { changeId } = req.params;
    const change = await AssetChangeLog.findByPk(changeId, {
      include: [
        {
          model: Employee,
          as: 'creator',
          attributes: ['id', 'name']
        },
        {
          model: Asset,
          as: 'asset',
          attributes: ['id', 'asset_id'],
          include: [
            {
              model: Enterprise,
              as: 'enterprise',
              attributes: ['id', 'name']
            },
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'product_name', 'version_name']
            }
          ]
        }
      ]
    });

    if (!change) {
      return res.status(404).json({ message: '未找到指定的变更记录' });
    }

    // 获取关联的订单信息
    const relatedOrders = await OrderHead.findAll({
      where: { asset_id: change.asset_id },
      include: [
        {
          model: Employee,
          as: 'creator',
          attributes: ['id', 'name']
        }
      ],
      attributes: ['id', 'order_id', 'created_at', 'actual_amount', 'payment_status', 'order_type'],
      order: [['created_at', 'DESC']]
    });

    // 将关联订单信息添加到响应中
    const responseData = {
      ...change.toJSON(),
      related_orders: relatedOrders
    };

    res.status(200).json(responseData);
  } catch (error) {
    console.error('获取变更记录详情时出错:', error);
    res.status(500).json({ message: '获取变更记录详情失败', error: error.message });
  }
};

// 删除资产
exports.deleteAsset = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await Asset.destroy({ where: { id: id } });
    if (result) {
      res.status(204).send();
    } else {
      res.status(404).json({ message: '未找到指定资产' });
    }
  } catch (error) {
    console.error('删除资产时出错:', error);
    res.status(500).json({ message: '删除资产失败', error: error.message });
  }
}; 