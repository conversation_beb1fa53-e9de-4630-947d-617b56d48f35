<template>
  <div class="product-order-form">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="handleBack" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h2>{{ pageTitle }}</h2>
      </div>
      <div class="header-actions">
        <el-button v-if="mode === 'view'" type="primary" @click="handleEdit">
          编辑
        </el-button>
        <el-button 
          v-if="isEditing" 
          type="success" 
          :loading="saving"
          @click="handleSave"
        >
          保存
        </el-button>
        <el-button v-if="isEditing" @click="handleCancel">
          取消
        </el-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content" v-loading="loading">
      <!-- 订单表头 -->
      <OrderHeader
        v-model="orderData"
        :is-editing="isEditing"
        :mode="mode"
        ref="orderHeaderRef"
      />

      <!-- 标签页内容 -->
      <div class="tabs-section">
        <el-tabs v-model="activeTab" class="form-tabs" type="border-card">
          <!-- 产品信息 -->
          <el-tab-pane name="product">
            <template #label>
              <span class="tab-label">
                <el-icon><Box /></el-icon>
                产品信息
              </span>
            </template>
            <div class="tab-content">
              <!-- 产品信息 -->
              <ProductInfo
                v-model="orderData.productItem"
                :is-editing="isEditing"
                ref="productInfoRef"
              />
            </div>
          </el-tab-pane>

          <!-- 附件管理 -->
          <el-tab-pane name="attachments">
            <template #label>
              <span class="tab-label">
                <el-icon><Paperclip /></el-icon>
                附件管理
              </span>
            </template>
            <OrderAttachment
              v-if="orderId"
              :order-id="orderId"
              :is-editing="isEditing"
              :allow-upload="true"
            />
            <div v-else class="attachment-placeholder">
              <el-empty description="请先保存订单，然后才能管理附件"></el-empty>
            </div>
          </el-tab-pane>

          <!-- 审核操作（仅审核模式显示） -->
          <el-tab-pane
            v-if="mode === 'review'"
            name="review"
          >
            <template #label>
              <span class="tab-label">
                <el-icon><Select /></el-icon>
                审核操作
              </span>
            </template>
            <ReviewActions
              :order-id="orderId"
              :audit-status="orderData.audit_status"
              :reject-reason="orderData.remark"
              :order-data="orderData"
              @audit-success="handleAuditSuccess"
            @delete-success="handleDeleteSuccess"
            @edit-mode="handleEditMode"
          />
        </el-tab-pane>
      </el-tabs>
      </div>

      <!-- 表尾信息 -->
      <div class="form-footer">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="orderData.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
                :disabled="!isEditing"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="制单人">
              <el-input
                :value="orderData.creator?.name || ''"
                disabled
                placeholder="系统自动填充"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="制单时间">
              <el-input
                v-model="formattedCreatedAt"
                disabled
                placeholder="系统自动填充"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowLeft, Box, Paperclip, Select } from '@element-plus/icons-vue';

// 组件引入
import OrderHeader from '../components/OrderHeader.vue';
import ProductInfo from '../components/ProductInfo.vue';
import OrderAttachment from '../components/OrderAttachment.vue';
import ReviewActions from '../components/ReviewActions.vue';

// API引入
import {
  getOrderById,
  createProductOrder,
  updateOrder
} from '@/api/order';

// 工具函数引入
import { formatDateTime } from '@/utils/format';

// 路由
const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const orderId = ref(route.params.id);
// 根据路由名称和参数判断模式
const mode = ref('view'); // create, edit, view, review

// 初始化模式
const initMode = () => {
  if (route.name === 'ProductOrderCreate') {
    mode.value = 'create';
  } else if (route.query.mode) {
    mode.value = route.query.mode; // 兼容旧的查询参数方式
  } else {
    mode.value = orderId.value ? 'view' : 'create';
  }
};
const activeTab = ref('product');

// 表单引用
const orderHeaderRef = ref(null);
const productInfoRef = ref(null);

// 订单数据
const orderData = ref({
  order_id: '',
  order_category: '产品订单',
  enterprise_id: null,
  asset_id: null,
  user_id: null,
  creation_method: '手工创建',
  order_type: '普通订单',
  standard_amount: 0,
  actual_amount: 0,
  tax_amount: null,
  invoice_type: '不开票',
  payment_status: '待支付',
  payment_method: '在线',
  payment_time: null,
  is_partner_order: false,
  partner_id: null,
  commission_ratio: null,
  commission_amount: null,
  commission_status: '未发放',
  audit_status: '待审核',
  remark: '',
  productItem: {
    product_id: null,
    user_count: 1,
    account_count: 1,
    duration_months: 12,
    selected_features: [],
    standard_price: 0,
    discount_rate: 1.0000,
    other_discount: 0,
    actual_price: 0,
    activity_other: ''
  }
});

// 原始数据备份（用于取消编辑）
const originalData = ref({});

// 计算属性
const pageTitle = computed(() => {
  switch (mode.value) {
    case 'create':
      return '新增产品订单';
    case 'edit':
      return '编辑产品订单';
    case 'review':
      return '审核产品订单';
    case 'view':
    default:
      return '查看产品订单';
  }
});

const isEditing = computed(() => {
  return mode.value === 'create' || mode.value === 'edit' || mode.value === 'review';
});

const formattedCreatedAt = computed(() => {
  return formatDateTime(orderData.value.created_at);
});

// 监听产品信息变化，更新订单金额
watch(() => orderData.value.productItem, (productItem) => {
  console.log('监听到产品信息变化:', productItem);
  if (productItem && (productItem.standard_price !== undefined || productItem.actual_price !== undefined)) {
    // 更新标准价格
    if (productItem.standard_price !== undefined) {
      orderData.value.standard_amount = productItem.standard_price || 0;
    }
    // 更新实付价格
    if (productItem.actual_price !== undefined) {
      orderData.value.actual_amount = productItem.actual_price || 0;
    }
    console.log('更新后的订单金额:', {
      standard_amount: orderData.value.standard_amount,
      actual_amount: orderData.value.actual_amount
    });
  }
}, { deep: true });

// 额外监听产品信息的价格字段变化
watch(() => [
  orderData.value.productItem?.standard_price,
  orderData.value.productItem?.actual_price
], ([standardPrice, actualPrice]) => {
  console.log('监听到价格字段变化:', { standardPrice, actualPrice });
  if (standardPrice !== undefined && standardPrice !== null) {
    orderData.value.standard_amount = parseFloat(standardPrice) || 0;
  }
  if (actualPrice !== undefined && actualPrice !== null) {
    orderData.value.actual_amount = parseFloat(actualPrice) || 0;
  }
}, { immediate: true });

// 监听orderData中企业和用户ID的变化
watch(() => [orderData.value.enterprise_id, orderData.value.user_id], ([enterpriseId, userId]) => {
  console.log('ProductOrderForm orderData 企业/用户ID变化:', {
    enterprise_id: enterpriseId,
    user_id: userId,
    enterprise_type: typeof enterpriseId,
    user_type: typeof userId
  });
}, { immediate: true });

// 获取订单详情
const fetchOrderData = async () => {
  if (!orderId.value || mode.value === 'create') return;
  
  loading.value = true;
  try {
    const response = await getOrderById(orderId.value);
    orderData.value = {
      ...orderData.value,
      ...response,
      productItem: response.productItems?.[0] || orderData.value.productItem
    };
    
    // 备份原始数据
    originalData.value = JSON.parse(JSON.stringify(orderData.value));
  } catch (error) {
    console.error('获取订单详情失败:', error);
    ElMessage.error('获取订单详情失败');
  } finally {
    loading.value = false;
  }
};

// 初始化新订单
const initNewOrder = async () => {
  if (mode.value !== 'create') return;

  // 订单号由 OrderHeader 组件自动生成，这里不需要重复调用
  console.log('初始化新产品订单');
};

// 表单验证
const validateForm = async () => {
  const validations = await Promise.all([
    orderHeaderRef.value?.validate(),
    productInfoRef.value?.validate()
  ]);

  return validations.every(result => result);
};

// 保存订单
const handleSave = async () => {
  // 添加详细的调试信息
  console.log('保存前的完整订单数据:', {
    user_id: orderData.value.user_id,
    enterprise_id: orderData.value.enterprise_id,
    user_id_type: typeof orderData.value.user_id,
    enterprise_id_type: typeof orderData.value.enterprise_id,
    orderData: orderData.value
  });

  const isValid = await validateForm();
  if (!isValid) {
    ElMessage.error('请检查表单信息');
    return;
  }

  // 验证必要的业务字段
  if (!orderData.value.user_id && !orderData.value.enterprise_id) {
    console.error('业务字段验证失败:', {
      user_id: orderData.value.user_id,
      enterprise_id: orderData.value.enterprise_id
    });
    ElMessage.error('必须选择用户或企业');
    return;
  }

  saving.value = true;
  try {
    if (mode.value === 'create') {
      // 创建新订单前，打印调试信息
      console.log('准备创建产品订单，数据:', {
        user_id: orderData.value.user_id,
        enterprise_id: orderData.value.enterprise_id,
        productItem: orderData.value.productItem,
        standard_amount: orderData.value.standard_amount,
        actual_amount: orderData.value.actual_amount
      });

      const response = await createProductOrder(orderData.value);
      orderId.value = response.id;
      ElMessage.success('创建成功');

      // 跳转到查看模式
      router.replace({
        name: 'ProductOrderForm',
        params: { id: response.id },
        query: { mode: 'view' }
      });
    } else {
      // 更新订单
      await updateOrder(orderId.value, orderData.value);
      ElMessage.success('保存成功');

      // 更新原始数据
      originalData.value = JSON.parse(JSON.stringify(orderData.value));

      if (mode.value === 'edit') {
        mode.value = 'view';
      }
    }
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`);
  } finally {
    saving.value = false;
  }
};

// 取消编辑
const handleCancel = async () => {
  if (mode.value === 'create') {
    handleBack();
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      '确定要取消编辑吗？未保存的更改将丢失。',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '继续编辑',
        type: 'warning',
      }
    );
    
    // 恢复原始数据
    orderData.value = JSON.parse(JSON.stringify(originalData.value));
    mode.value = 'view';
  } catch (error) {
    // 用户取消
  }
};

// 编辑模式
const handleEdit = () => {
  mode.value = 'edit';
};

// 返回
const handleBack = () => {
  router.back();
};

// 审核成功回调
const handleAuditSuccess = (action) => {
  // 审核成功后刷新数据
  fetchOrderData();
};

// 删除成功回调
const handleDeleteSuccess = () => {
  // 删除成功后返回列表
  router.push({ name: 'OrderReviewList' });
};

// 切换到编辑模式（从审核操作触发）
const handleEditMode = () => {
  mode.value = 'edit';
  activeTab.value = 'product';
};

// 组件挂载时初始化
onMounted(async () => {
  initMode(); // 先初始化模式
  if (mode.value === 'create') {
    await initNewOrder();
  } else {
    await fetchOrderData();
  }
});
</script>

<style scoped>
.product-order-form {
  padding: 20px;
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  padding: 8px 12px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.form-content {
  max-width: 1200px;
  margin: 0 auto;
}

.tabs-section {
  margin-top: 20px;
}

.form-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-content {
  padding: 20px 0;
}

.attachment-placeholder {
  padding: 40px 0;
  text-align: center;
}

.form-footer {
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-top: 3px solid #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-order-form {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-left {
    justify-content: center;
  }
  
  .header-actions {
    justify-content: center;
  }
}
</style>
