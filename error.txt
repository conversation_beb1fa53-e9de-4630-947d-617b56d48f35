监听到价格字段变化: Object
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm orderData 企业/用户ID变化: Object
ReviewActions-DOCOryAq.js:1 Props更新，接收到的数据: Proxy(Object)
ReviewActions-DOCOryAq.js:1 当前formData: Proxy(Object)
ReviewActions-DOCOryAq.js:1 合并后的formData: Proxy(Object)
ReviewActions-DOCOryAq.js:1 OrderHeader 组件挂载，当前状态: Object
ReviewActions-DOCOryAq.js:1 跳过订单编码生成，原因: Object
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm 组件挂载，当前模式: create 是否编辑: true
ProductOrderForm-DdaTjd0p.js:1 初始化新产品订单
ReviewActions-DOCOryAq.js:1 OrderHeader updateParent 被调用，当前数据: Object
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: Object
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: Object
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm orderData 企业/用户ID变化: Object
ReviewActions-DOCOryAq.js:1 监听到 composable 选择变化: Object
ReviewActions-DOCOryAq.js:1 更新 formData.enterprise_id 为: 9
ReviewActions-DOCOryAq.js:1 Props更新，接收到的数据: Proxy(Object)
ReviewActions-DOCOryAq.js:1 当前formData: Proxy(Object)
ReviewActions-DOCOryAq.js:1 保留当前enterprise_id: 9
ReviewActions-DOCOryAq.js:1 合并后的formData: Proxy(Object)
ReviewActions-DOCOryAq.js:1 企业字段验证: Object
ReviewActions-DOCOryAq.js:1 监听到 composable 选择变化: Object
ReviewActions-DOCOryAq.js:1 更新 formData.user_id 为: 1
ReviewActions-DOCOryAq.js:1 OrderHeader updateParent 被调用，当前数据: Object
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: Object
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: Object
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm orderData 企业/用户ID变化: Object
ReviewActions-DOCOryAq.js:1 Props更新，接收到的数据: Proxy(Object)
ReviewActions-DOCOryAq.js:1 当前formData: Proxy(Object)
ReviewActions-DOCOryAq.js:1 合并后的formData: Proxy(Object)
ReviewActions-DOCOryAq.js:1 用户字段验证: Object
ReviewActions-DOCOryAq.js:1 OrderHeader updateParent 被调用，当前数据: Object
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: Object
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: Object
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm orderData 企业/用户ID变化: Object
ReviewActions-DOCOryAq.js:1 Props更新，接收到的数据: Proxy(Object)
ReviewActions-DOCOryAq.js:1 当前formData: Proxy(Object)
ReviewActions-DOCOryAq.js:1 合并后的formData: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: Object
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: Object
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: Object
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: Object
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: Object
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: Object
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: Object
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: Object
ProductOrderForm-DdaTjd0p.js:1 保存前的完整订单数据: Object
ReviewActions-DOCOryAq.js:1 OrderHeader 验证前的数据: Object
ReviewActions-DOCOryAq.js:1 企业字段验证: Object
ReviewActions-DOCOryAq.js:1 用户字段验证: Object
ProductOrderForm-DdaTjd0p.js:1 准备创建产品订单，数据: Object
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object)
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: Object
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: Object
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm orderData 企业/用户ID变化: Object
ReviewActions-DOCOryAq.js:1 Props更新，接收到的数据: Proxy(Object)
ReviewActions-DOCOryAq.js:1 当前formData: Proxy(Object)
ReviewActions-DOCOryAq.js:1 合并后的formData: Proxy(Object)[[Handler]]: T1[[Target]]: Object[[IsRevoked]]: false
index-BpvCjo1f.js:59  API Error:  xt
(匿名) @ index-BpvCjo1f.js:59
ReviewActions-DOCOryAq.js:1  获取附件列表失败: xtcode: "ECONNABORTED"config: {transitional: {…}, adapter: Array(3), transformRequest: Array(1), transformResponse: Array(1), timeout: 5000, …}message: "timeout of 5000ms exceeded"name: "AxiosError"request: XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 5000, withCredentials: false, upload: XMLHttpRequestUpload, …}stack: "AxiosError: timeout of 5000ms exceeded\n    at y.ontimeout (https://admin.bogoo.net/assets/index-BpvCjo1f.js:20:6298)\n    at Ga.request (https://admin.bogoo.net/assets/index-BpvCjo1f.js:22:2069)\n    at async q (https://admin.bogoo.net/assets/ReviewActions-DOCOryAq.js:1:17157)"[[Prototype]]: Error
q @ ReviewActions-DOCOryAq.js:1
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-DOCOryAq.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-DOCOryAq.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-DOCOryAq.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-DOCOryAq.js:1 OrderHeader 组件挂载，当前状态: {mode: 'view', isEditing: false, order_id: '', order_category: '产品订单'}
ReviewActions-DOCOryAq.js:1 跳过订单编码生成，原因: {mode: 'view', isEditing: false, hasOrderId: false}
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm 组件挂载，当前模式: create 是否编辑: true
ProductOrderForm-DdaTjd0p.js:1 初始化新产品订单
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
o @ asset-BCTu60HN.js:1
R @ ReviewActions-DOCOryAq.js:1
initialize @ ReviewActions-DOCOryAq.js:1
(匿名) @ ReviewActions-DOCOryAq.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:40
p @ index-BpvCjo1f.js:40
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
$ @ product-BtcT97jo.js:1
Z @ ProductOrderForm-DdaTjd0p.js:1
(匿名) @ ProductOrderForm-DdaTjd0p.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:40
p @ index-BpvCjo1f.js:40
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
bte @ index-BpvCjo1f.js:63
F @ ReviewActions-DOCOryAq.js:1
initialize @ ReviewActions-DOCOryAq.js:1
(匿名) @ ReviewActions-DOCOryAq.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:40
p @ index-BpvCjo1f.js:40
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
hte @ index-BpvCjo1f.js:63
U @ ReviewActions-DOCOryAq.js:1
initialize @ ReviewActions-DOCOryAq.js:1
(匿名) @ ReviewActions-DOCOryAq.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:40
p @ index-BpvCjo1f.js:40
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
ReviewActions-DOCOryAq.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: null, user_id: null, formData: Proxy(Object)}
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-DOCOryAq.js:1 监听到 composable 选择变化: {enterpriseId: 9, userId: 1, assetId: 1, currentFormData: {…}}
ReviewActions-DOCOryAq.js:1 更新 formData.enterprise_id 为: 9
ReviewActions-DOCOryAq.js:1 更新 formData.user_id 为: 1
ReviewActions-DOCOryAq.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: 1, user_id: null, …}
ReviewActions-DOCOryAq.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-DOCOryAq.js:1 保留当前enterprise_id: 9
ReviewActions-DOCOryAq.js:1 保留当前user_id: 1
ReviewActions-DOCOryAq.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-DOCOryAq.js:1 企业字段验证: {value: 9, enterpriseId: 9, userId: 1, enterpriseType: 'number', userType: 'number'}
ReviewActions-DOCOryAq.js:1 用户字段验证: {value: 1, userId: 1, enterpriseId: 9, userType: 'number', enterpriseType: 'number'}
ReviewActions-DOCOryAq.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 9, user_id: 1, formData: Proxy(Object)}
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 9, user_id: 1, enterprise_type: 'number', user_type: 'number'}
ReviewActions-DOCOryAq.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-DOCOryAq.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-DOCOryAq.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products/3/features"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
a @ product-BtcT97jo.js:1
A @ product-BtcT97jo.js:1
K @ ProductOrderForm-DdaTjd0p.js:1
Q @ ProductOrderForm-DdaTjd0p.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
mt @ index-BpvCjo1f.js:40
ye @ index-BpvCjo1f.js:40
m @ index-BpvCjo1f.js:40
Ge.n.<computed>.n.<computed> @ index-BpvCjo1f.js:18
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: 1998, actual_amount: 1998}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 1998, actualPrice: 1998}
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: 1998, actual_amount: 1998}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 1998, actualPrice: 1998}
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/pricing/calculate"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
se @ ProductOrderForm-DdaTjd0p.js:1
E @ ProductOrderForm-DdaTjd0p.js:1
Q @ ProductOrderForm-DdaTjd0p.js:1
await in Q
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
mt @ index-BpvCjo1f.js:40
ye @ index-BpvCjo1f.js:40
m @ index-BpvCjo1f.js:40
Ge.n.<computed>.n.<computed> @ index-BpvCjo1f.js:18
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: 2048, actual_amount: 2048}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 2048, actualPrice: 2048}
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: 2048, actual_amount: 2048}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 2048, actualPrice: 2048}
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/pricing/calculate"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
se @ ProductOrderForm-DdaTjd0p.js:1
E @ ProductOrderForm-DdaTjd0p.js:1
ee @ ProductOrderForm-DdaTjd0p.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
_ @ index-BpvCjo1f.js:40
b @ index-BpvCjo1f.js:40
i @ index-BpvCjo1f.js:29
(匿名) @ index-BpvCjo1f.js:29
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: 2098, actual_amount: 2098}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 2098, actualPrice: 2098}
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: 2098, actual_amount: 2098}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 2098, actualPrice: 2098}
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/pricing/calculate"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
se @ ProductOrderForm-DdaTjd0p.js:1
E @ ProductOrderForm-DdaTjd0p.js:1
ee @ ProductOrderForm-DdaTjd0p.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
_ @ index-BpvCjo1f.js:40
b @ index-BpvCjo1f.js:40
i @ index-BpvCjo1f.js:29
(匿名) @ index-BpvCjo1f.js:29
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 24, selected_features: Array(0), …}
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 24, selected_features: Proxy(Array), …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: 4196, actual_amount: 4196}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 4196, actualPrice: 4196}
ProductOrderForm-DdaTjd0p.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 24, selected_features: Array(0), …}
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {product_id: 3, user_count: 3, account_count: 7, duration_months: 24, selected_features: Proxy(Array), …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: 4196, actual_amount: 4196}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 4196, actualPrice: 4196}
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/pricing/calculate"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
se @ ProductOrderForm-DdaTjd0p.js:1
E @ ProductOrderForm-DdaTjd0p.js:1
X @ ProductOrderForm-DdaTjd0p.js:1
onClick @ ProductOrderForm-DdaTjd0p.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
ProductOrderForm-DdaTjd0p.js:1 保存前的完整订单数据: {user_id: 1, enterprise_id: 9, user_id_type: 'number', enterprise_id_type: 'number', orderData: Proxy(Object)}
ReviewActions-DOCOryAq.js:1 OrderHeader 验证前的数据: {enterprise_id: 9, user_id: 1, enterprise_id_type: 'number', user_id_type: 'number', selectedEnterprise: 9, …}
ReviewActions-DOCOryAq.js:1 企业字段验证: {value: 9, enterpriseId: 9, userId: 1, enterpriseType: 'number', userType: 'number'}
ReviewActions-DOCOryAq.js:1 用户字段验证: {value: 1, userId: 1, enterpriseId: 9, userType: 'number', enterpriseType: 'number'}
ProductOrderForm-DdaTjd0p.js:1 准备创建产品订单，数据: {user_id: 1, enterprise_id: 9, productItem: Proxy(Object), standard_amount: 4196, actual_amount: 4196}
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/orders/product"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
$ @ order-Dz6V7Dvr.js:1
Q @ ProductOrderForm-DdaTjd0p.js:1
await in Q
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
m @ order-Dz6V7Dvr.js:1
q @ ReviewActions-DOCOryAq.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
Zv.i.call @ index-BpvCjo1f.js:14
b @ index-BpvCjo1f.js:10
vT @ index-BpvCjo1f.js:10
Zv @ index-BpvCjo1f.js:14
fe @ index-BpvCjo1f.js:14
setup @ ReviewActions-DOCOryAq.js:1
Yi @ index-BpvCjo1f.js:14
u$ @ index-BpvCjo1f.js:14
i$ @ index-BpvCjo1f.js:14
G @ index-BpvCjo1f.js:14
W @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
J @ index-BpvCjo1f.js:14
I @ index-BpvCjo1f.js:14
M @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
x @ index-BpvCjo1f.js:14
T @ index-BpvCjo1f.js:14
S @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
ve @ index-BpvCjo1f.js:14
run @ index-BpvCjo1f.js:10
runIfDirty @ index-BpvCjo1f.js:10
Yi @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
jv @ index-BpvCjo1f.js:14
P.$e.scheduler @ index-BpvCjo1f.js:14
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
Q @ ProductOrderForm-DdaTjd0p.js:1
await in Q
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
m @ order-Dz6V7Dvr.js:1
q @ ReviewActions-DOCOryAq.js:1
(匿名) @ ReviewActions-DOCOryAq.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
jv @ index-BpvCjo1f.js:14
P.$e.scheduler @ index-BpvCjo1f.js:14
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
Q @ ProductOrderForm-DdaTjd0p.js:1
await in Q
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {id: 13, order_id: 21, product_id: 3, user_count: 3, account_count: 7, …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: '4196.00', actual_amount: '4196.00'}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: '4196.00', actualPrice: '4196.00'}
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 9, user_id: 1, enterprise_type: 'number', user_type: 'number'}
ReviewActions-DOCOryAq.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: 'PO20250728341', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-DOCOryAq.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-DOCOryAq.js:1 合并后的formData: Proxy(Object) {order_id: 'PO20250728341', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
i @ order-Dz6V7Dvr.js:1
b @ ProductOrderForm-DdaTjd0p.js:1
Q @ ProductOrderForm-DdaTjd0p.js:1
await in Q
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/orders/21/attachments"。
XMLHttpRequest.send @ inspector.js:7
vJ @ index-BpvCjo1f.js:58
d @ index-BpvCjo1f.js:58
await in d
u @ index-BpvCjo1f.js:58
await in u
i @ index-BpvCjo1f.js:58
f @ index-BpvCjo1f.js:58
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
m @ order-Dz6V7Dvr.js:1
q @ ReviewActions-DOCOryAq.js:1
ee @ ReviewActions-DOCOryAq.js:1
c @ index-BpvCjo1f.js:58
onSuccess @ index-BpvCjo1f.js:58
(匿名) @ index-BpvCjo1f.js:58
XMLHttpRequest.send
XMLHttpRequest.send @ inspector.js:7
vJ @ index-BpvCjo1f.js:58
d @ index-BpvCjo1f.js:58
await in d
u @ index-BpvCjo1f.js:58
await in u
i @ index-BpvCjo1f.js:58
f @ index-BpvCjo1f.js:58
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/review"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
o @ order-Dz6V7Dvr.js:1
y @ OrderReviewList-DKzt0TV0.js:1
(匿名) @ OrderReviewList-DKzt0TV0.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:40
p @ index-BpvCjo1f.js:40
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-DOCOryAq.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-DOCOryAq.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-DOCOryAq.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-DOCOryAq.js:1 OrderHeader 组件挂载，当前状态: {mode: 'view', isEditing: false, order_id: '', order_category: '产品订单'}
ReviewActions-DOCOryAq.js:1 跳过订单编码生成，原因: {mode: 'view', isEditing: false, hasOrderId: false}
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm 组件挂载，当前模式: edit 是否编辑: true
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
o @ asset-BCTu60HN.js:1
R @ ReviewActions-DOCOryAq.js:1
initialize @ ReviewActions-DOCOryAq.js:1
(匿名) @ ReviewActions-DOCOryAq.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ OrderReviewList-DKzt0TV0.js:1
onClick @ OrderReviewList-DKzt0TV0.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
hte @ index-BpvCjo1f.js:63
U @ ReviewActions-DOCOryAq.js:1
initialize @ ReviewActions-DOCOryAq.js:1
(匿名) @ ReviewActions-DOCOryAq.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ OrderReviewList-DKzt0TV0.js:1
onClick @ OrderReviewList-DKzt0TV0.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
bte @ index-BpvCjo1f.js:63
F @ ReviewActions-DOCOryAq.js:1
initialize @ ReviewActions-DOCOryAq.js:1
(匿名) @ ReviewActions-DOCOryAq.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ OrderReviewList-DKzt0TV0.js:1
onClick @ OrderReviewList-DKzt0TV0.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
m @ order-Dz6V7Dvr.js:1
q @ ReviewActions-DOCOryAq.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
Zv.i.call @ index-BpvCjo1f.js:14
b @ index-BpvCjo1f.js:10
vT @ index-BpvCjo1f.js:10
Zv @ index-BpvCjo1f.js:14
fe @ index-BpvCjo1f.js:14
setup @ ReviewActions-DOCOryAq.js:1
Yi @ index-BpvCjo1f.js:14
u$ @ index-BpvCjo1f.js:14
i$ @ index-BpvCjo1f.js:14
G @ index-BpvCjo1f.js:14
W @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
$ @ index-BpvCjo1f.js:14
M @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
$ @ index-BpvCjo1f.js:14
_ @ index-BpvCjo1f.js:14
S @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
ve @ index-BpvCjo1f.js:14
run @ index-BpvCjo1f.js:10
P @ index-BpvCjo1f.js:14
G @ index-BpvCjo1f.js:14
W @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
$ @ index-BpvCjo1f.js:14
M @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
$ @ index-BpvCjo1f.js:14
_ @ index-BpvCjo1f.js:14
S @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
$ @ index-BpvCjo1f.js:14
_ @ index-BpvCjo1f.js:14
S @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
ve @ index-BpvCjo1f.js:14
run @ index-BpvCjo1f.js:10
P @ index-BpvCjo1f.js:14
G @ index-BpvCjo1f.js:14
W @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
$ @ index-BpvCjo1f.js:14
_ @ index-BpvCjo1f.js:14
S @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
$ @ index-BpvCjo1f.js:14
_ @ index-BpvCjo1f.js:14
S @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
$ @ index-BpvCjo1f.js:14
_ @ index-BpvCjo1f.js:14
S @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
ve @ index-BpvCjo1f.js:14
run @ index-BpvCjo1f.js:10
P @ index-BpvCjo1f.js:14
G @ index-BpvCjo1f.js:14
W @ index-BpvCjo1f.js:14
h @ index-BpvCjo1f.js:14
ve @ index-BpvCjo1f.js:14
run @ index-BpvCjo1f.js:10
runIfDirty @ index-BpvCjo1f.js:10
Yi @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ OrderReviewList-DKzt0TV0.js:1
onClick @ OrderReviewList-DKzt0TV0.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
$ @ product-BtcT97jo.js:1
Z @ ProductOrderForm-DdaTjd0p.js:1
(匿名) @ ProductOrderForm-DdaTjd0p.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ OrderReviewList-DKzt0TV0.js:1
onClick @ OrderReviewList-DKzt0TV0.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
ProductOrderForm-DdaTjd0p.js:1 监听到产品信息变化: Proxy(Object) {id: 13, order_id: 21, product_id: 3, user_count: 3, account_count: 7, …}
ProductOrderForm-DdaTjd0p.js:1 更新后的订单金额: {standard_amount: '4196.00', actual_amount: '4196.00'}
ProductOrderForm-DdaTjd0p.js:1 监听到价格字段变化: {standardPrice: '4196.00', actualPrice: '4196.00'}
ProductOrderForm-DdaTjd0p.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 9, user_id: 1, enterprise_type: 'number', user_type: 'number'}
ReviewActions-DOCOryAq.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: 'PO20250728341', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-DOCOryAq.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-DOCOryAq.js:1 合并后的formData: Proxy(Object) {order_id: 'PO20250728341', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-DOCOryAq.js:1 监听到 composable 选择变化: {enterpriseId: 9, userId: 1, assetId: 1, currentFormData: {…}}
ReviewActions-DOCOryAq.js:1 企业字段验证: {value: 9, enterpriseId: 9, userId: 1, enterpriseType: 'number', userType: 'number'}
ReviewActions-DOCOryAq.js:1 用户字段验证: {value: 1, userId: 1, enterpriseId: 9, userType: 'number', enterpriseType: 'number'}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
i @ order-Dz6V7Dvr.js:1
b @ ProductOrderForm-DdaTjd0p.js:1
(匿名) @ ProductOrderForm-DdaTjd0p.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ OrderReviewList-DKzt0TV0.js:1
onClick @ OrderReviewList-DKzt0TV0.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
m @ order-Dz6V7Dvr.js:1
q @ ReviewActions-DOCOryAq.js:1
(匿名) @ ReviewActions-DOCOryAq.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ OrderReviewList-DKzt0TV0.js:1
onClick @ OrderReviewList-DKzt0TV0.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
hte @ index-BpvCjo1f.js:63
U @ AssetList-DAvhq6cY.js:1
(匿名) @ AssetList-DAvhq6cY.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:40
p @ index-BpvCjo1f.js:40
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
o @ asset-BCTu60HN.js:1
h @ useAssetData-CDWCdF9s.js:1
v @ AssetList-DAvhq6cY.js:1
(匿名) @ AssetList-DAvhq6cY.js:1
await in (匿名)
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:40
p @ index-BpvCjo1f.js:40
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
hte @ index-BpvCjo1f.js:63
X @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
$ @ product-BtcT97jo.js:1
X @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
bte @ index-BpvCjo1f.js:63
X @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7   GET https://service.bogoo.net/api/assets/1 500 (Internal Server Error)
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
d @ asset-BCTu60HN.js:1
_ @ useAssetData-CDWCdF9s.js:1
M @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
await in l
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
index-BpvCjo1f.js:59  API Error:  xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
(匿名) @ index-BpvCjo1f.js:59
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
d @ asset-BCTu60HN.js:1
_ @ useAssetData-CDWCdF9s.js:1
M @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
await in l
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 加载失败:GET "https://service.bogoo.net/api/assets/1".
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
d @ asset-BCTu60HN.js:1
_ @ useAssetData-CDWCdF9s.js:1
M @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
await in l
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
AssetDetail-BRUe2xiY.js:3  Uncaught (in promise) xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
n2 @ index-BpvCjo1f.js:20
g @ index-BpvCjo1f.js:20
XMLHttpRequest.send
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
d @ asset-BCTu60HN.js:1
_ @ useAssetData-CDWCdF9s.js:1
M @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
await in l
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets/changes?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
l @ asset-BCTu60HN.js:1
_ @ AssetChangeList-CORVnnZg.js:1
(匿名) @ AssetChangeList-CORVnnZg.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:40
p @ index-BpvCjo1f.js:40
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
hte @ index-BpvCjo1f.js:63
U @ AssetList-DAvhq6cY.js:1
(匿名) @ AssetList-DAvhq6cY.js:1
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:40
p @ index-BpvCjo1f.js:40
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
o @ asset-BCTu60HN.js:1
h @ useAssetData-CDWCdF9s.js:1
v @ AssetList-DAvhq6cY.js:1
(匿名) @ AssetList-DAvhq6cY.js:1
await in (匿名)
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:40
p @ index-BpvCjo1f.js:40
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
hte @ index-BpvCjo1f.js:63
X @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
$ @ product-BtcT97jo.js:1
X @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
Ga.<computed> @ index-BpvCjo1f.js:23
(匿名) @ index-BpvCjo1f.js:18
bte @ index-BpvCjo1f.js:63
X @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7   GET https://service.bogoo.net/api/assets/1 500 (Internal Server Error)
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
d @ asset-BCTu60HN.js:1
_ @ useAssetData-CDWCdF9s.js:1
M @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
await in l
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
index-BpvCjo1f.js:59  API Error:  xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
(匿名) @ index-BpvCjo1f.js:59
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
d @ asset-BCTu60HN.js:1
_ @ useAssetData-CDWCdF9s.js:1
M @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
await in l
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
AssetDetail-BRUe2xiY.js:3  Uncaught (in promise) xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
n2 @ index-BpvCjo1f.js:20
g @ index-BpvCjo1f.js:20
XMLHttpRequest.send
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BpvCjo1f.js:20
xhr @ index-BpvCjo1f.js:20
Xg @ index-BpvCjo1f.js:22
Promise.then
_request @ index-BpvCjo1f.js:23
request @ index-BpvCjo1f.js:22
(匿名) @ index-BpvCjo1f.js:18
d @ asset-BCTu60HN.js:1
_ @ useAssetData-CDWCdF9s.js:1
M @ AssetDetail-BRUe2xiY.js:3
l @ AssetDetail-BRUe2xiY.js:3
await in l
(匿名) @ AssetDetail-BRUe2xiY.js:3
(匿名) @ index-BpvCjo1f.js:14
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
t.__weh.t.__weh @ index-BpvCjo1f.js:14
A1 @ index-BpvCjo1f.js:14
L1 @ index-BpvCjo1f.js:14
Promise.then
P1 @ index-BpvCjo1f.js:14
gT @ index-BpvCjo1f.js:14
n$ @ index-BpvCjo1f.js:14
Zv.i.scheduler @ index-BpvCjo1f.js:14
d.scheduler @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
Bv @ index-BpvCjo1f.js:10
notify @ index-BpvCjo1f.js:10
trigger @ index-BpvCjo1f.js:10
set value @ index-BpvCjo1f.js:10
M @ index-BpvCjo1f.js:63
(匿名) @ index-BpvCjo1f.js:63
Promise.then
O @ index-BpvCjo1f.js:63
w @ index-BpvCjo1f.js:63
E @ AssetList-DAvhq6cY.js:1
onClick @ AssetList-DAvhq6cY.js:1
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
JT @ index-BpvCjo1f.js:14
handleClick @ index-BpvCjo1f.js:23
Yi @ index-BpvCjo1f.js:14
Wo @ index-BpvCjo1f.js:14
n @ index-BpvCjo1f.js:18
inspector.js:7 XHR 加载失败:GET "https://service.bogoo.net/api/assets/1".



root@iZ7xvczjzdx1cgmypn8ufmZ:~# pm2 logs customer-backend --lines 50
[TAILING] Tailing last 50 lines for [customer-backend] process (change the value with --lines option)
/var/www/customer_system/backend/logs/customer-backend-out.log last 50 lines:
4|customer | 2025-07-28 13:24:54 +08:00: 产品订单表体数据: { product_id: 3, order_id: 15, user_count: 3, account_count: 5 }
4|customer | 2025-07-28 13:40:05 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 13:40:05 +08:00:   user_id: 1,
4|customer | 2025-07-28 13:40:05 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 13:40:05 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 13:40:05 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 13:40:05 +08:00: }
4|customer | 2025-07-28 13:40:05 +08:00: 产品订单表体数据: { product_id: 3, order_id: 16, user_count: 3, account_count: 5 }
4|customer | 2025-07-28 13:51:23 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 13:51:23 +08:00:   user_id: 1,
4|customer | 2025-07-28 13:51:23 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 13:51:23 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 13:51:23 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 13:51:23 +08:00: }
4|customer | 2025-07-28 13:51:23 +08:00: 产品订单表体数据: { product_id: 3, order_id: 17, user_count: 3, account_count: 5 }
4|customer | 2025-07-28 14:06:30 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 14:06:30 +08:00:   user_id: 1,
4|customer | 2025-07-28 14:06:30 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 14:06:30 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 14:06:30 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 14:06:30 +08:00: }
4|customer | 2025-07-28 14:06:30 +08:00: 产品订单表体数据: { product_id: 3, order_id: 18, user_count: 3, account_count: 7 }
4|customer | 2025-07-28 14:11:19 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-28 14:11:19 +08:00: 数据库连接已关闭
4|customer | 2025-07-28 14:11:19 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-28 14:11:19 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-28 14:12:49 +08:00: 服务订单数据清理后: {
4|customer | 2025-07-28 14:12:49 +08:00:   user_id: 1,
4|customer | 2025-07-28 14:12:49 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 14:12:49 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 14:12:49 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 14:12:49 +08:00: }
4|customer | 2025-07-28 14:25:16 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-28 14:25:16 +08:00: 数据库连接已关闭
4|customer | 2025-07-28 14:25:17 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-28 14:25:17 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-28 14:26:01 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 14:26:01 +08:00:   user_id: 1,
4|customer | 2025-07-28 14:26:01 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 14:26:01 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 14:26:01 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 14:26:01 +08:00: }
4|customer | 2025-07-28 14:26:01 +08:00: 产品订单表体数据: { product_id: 3, order_id: 20, user_count: 3, account_count: 6 }
4|customer | 2025-07-28 14:26:39 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 14:26:39 +08:00:   user_id: 1,
4|customer | 2025-07-28 14:26:39 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 14:26:39 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 14:26:39 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 14:26:39 +08:00: }
4|customer | 2025-07-28 14:26:39 +08:00: 产品订单表体数据: { product_id: 3, order_id: 21, user_count: 3, account_count: 7 }

/var/www/customer_system/backend/logs/customer-backend-error.log last 50 lines:
4|customer | dAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_type` AS `orders.order_type`, `orders`.`total_amount` AS `orders.total_amount`, `orders`.`order_status` AS `orders.order_status`, `orders`.`createdAt` AS `orders.createdAt`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer | 2025-07-28 14:27:57 +08:00:     parameters: undefined
4|customer | 2025-07-28 14:27:57 +08:00:   },
4|customer | 2025-07-28 14:27:57 +08:00:   sql: "SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_ratio` AS `user.commission_ratio`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_type` AS `orders.order_type`, `orders`.`total_amount` AS `orders.total_amount`, `orders`.`order_status` AS `orders.order_status`, `orders`.`createdAt` AS `orders.createdAt`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer | 2025-07-28 14:27:57 +08:00:   parameters: {}
4|customer | 2025-07-28 14:27:57 +08:00: }
