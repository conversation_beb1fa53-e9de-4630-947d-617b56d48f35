module.exports = {
  apps: [
    {
      name: "customer-backend",
      script: "index.js",
      cwd: "./backend",
      interpreter: "/usr/bin/node",
      instances: 1,
      exec_mode: "fork",
      watch: false, // 生产环境不建议开启watch
      max_memory_restart: "1G",
      env: {
        PORT: 3002,
        NODE_ENV: "production"
      },
      env_production: {
        PORT: 3002,
        NODE_ENV: "production"
      },
      log_file: "./logs/customer-backend.log",
      error_file: "./logs/customer-backend-error.log",
      out_file: "./logs/customer-backend-out.log",
      log_date_format: "YYYY-MM-DD HH:mm:ss Z"
    }
  ]
};
