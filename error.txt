inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BoOgBszF.js:20
xhr @ index-BoOgBszF.js:20
Xg @ index-BoOgBszF.js:22
Promise.then
_request @ index-BoOgBszF.js:23
request @ index-BoOgBszF.js:22
Ga.<computed> @ index-BoOgBszF.js:23
(匿名) @ index-BoOgBszF.js:18
$ @ product-CEWOjI8N.js:1
f @ index-BoOgBszF.js:63
(匿名) @ index-BoOgBszF.js:14
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
t.__weh.t.__weh @ index-BoOgBszF.js:14
A1 @ index-BoOgBszF.js:14
L1 @ index-BoOgBszF.js:14
Promise.then
P1 @ index-BoOgBszF.js:14
gT @ index-BoOgBszF.js:14
n$ @ index-BoOgBszF.js:14
Zv.i.scheduler @ index-BoOgBszF.js:14
d.scheduler @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
Bv @ index-BoOgBszF.js:10
notify @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
set value @ index-BoOgBszF.js:10
M @ index-BoOgBszF.js:63
(匿名) @ index-BoOgBszF.js:63
Promise.then
O @ index-BoOgBszF.js:63
w @ index-BoOgBszF.js:63
install @ index-BoOgBszF.js:63
use @ index-BoOgBszF.js:14
(匿名) @ index-BoOgBszF.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BoOgBszF.js:20
xhr @ index-BoOgBszF.js:20
Xg @ index-BoOgBszF.js:22
Promise.then
_request @ index-BoOgBszF.js:23
request @ index-BoOgBszF.js:22
Ga.<computed> @ index-BoOgBszF.js:23
(匿名) @ index-BoOgBszF.js:18
hte @ index-BoOgBszF.js:63
U @ AssetList-CTJZCXDZ.js:1
(匿名) @ AssetList-CTJZCXDZ.js:1
(匿名) @ index-BoOgBszF.js:14
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
t.__weh.t.__weh @ index-BoOgBszF.js:14
A1 @ index-BoOgBszF.js:14
L1 @ index-BoOgBszF.js:14
Promise.then
P1 @ index-BoOgBszF.js:14
gT @ index-BoOgBszF.js:14
n$ @ index-BoOgBszF.js:14
Zv.i.scheduler @ index-BoOgBszF.js:14
d.scheduler @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
Bv @ index-BoOgBszF.js:10
notify @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
set value @ index-BoOgBszF.js:10
M @ index-BoOgBszF.js:63
(匿名) @ index-BoOgBszF.js:63
Promise.then
O @ index-BoOgBszF.js:63
w @ index-BoOgBszF.js:63
w @ index-BoOgBszF.js:40
p @ index-BoOgBszF.js:40
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
n @ index-BoOgBszF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets?page=1&pageSize=20&sortBy=createdAt&sortOrder=DESC"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BoOgBszF.js:20
xhr @ index-BoOgBszF.js:20
Xg @ index-BoOgBszF.js:22
Promise.then
_request @ index-BoOgBszF.js:23
request @ index-BoOgBszF.js:22
(匿名) @ index-BoOgBszF.js:18
o @ asset-CkmFDqt2.js:1
h @ useAssetData-cb_OZZLX.js:1
v @ AssetList-CTJZCXDZ.js:1
(匿名) @ AssetList-CTJZCXDZ.js:1
await in (匿名)
(匿名) @ index-BoOgBszF.js:14
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
t.__weh.t.__weh @ index-BoOgBszF.js:14
A1 @ index-BoOgBszF.js:14
L1 @ index-BoOgBszF.js:14
Promise.then
P1 @ index-BoOgBszF.js:14
gT @ index-BoOgBszF.js:14
n$ @ index-BoOgBszF.js:14
Zv.i.scheduler @ index-BoOgBszF.js:14
d.scheduler @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
Bv @ index-BoOgBszF.js:10
notify @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
set value @ index-BoOgBszF.js:10
M @ index-BoOgBszF.js:63
(匿名) @ index-BoOgBszF.js:63
Promise.then
O @ index-BoOgBszF.js:63
w @ index-BoOgBszF.js:63
w @ index-BoOgBszF.js:40
p @ index-BoOgBszF.js:40
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
n @ index-BoOgBszF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BoOgBszF.js:20
xhr @ index-BoOgBszF.js:20
Xg @ index-BoOgBszF.js:22
Promise.then
_request @ index-BoOgBszF.js:23
request @ index-BoOgBszF.js:22
Ga.<computed> @ index-BoOgBszF.js:23
(匿名) @ index-BoOgBszF.js:18
$ @ product-CEWOjI8N.js:1
X @ AssetDetail-B4AjOxye.js:3
l @ AssetDetail-B4AjOxye.js:3
(匿名) @ AssetDetail-B4AjOxye.js:3
(匿名) @ index-BoOgBszF.js:14
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
t.__weh.t.__weh @ index-BoOgBszF.js:14
A1 @ index-BoOgBszF.js:14
L1 @ index-BoOgBszF.js:14
Promise.then
P1 @ index-BoOgBszF.js:14
gT @ index-BoOgBszF.js:14
n$ @ index-BoOgBszF.js:14
Zv.i.scheduler @ index-BoOgBszF.js:14
d.scheduler @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
Bv @ index-BoOgBszF.js:10
notify @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
set value @ index-BoOgBszF.js:10
M @ index-BoOgBszF.js:63
(匿名) @ index-BoOgBszF.js:63
Promise.then
O @ index-BoOgBszF.js:63
w @ index-BoOgBszF.js:63
E @ AssetList-CTJZCXDZ.js:1
onClick @ AssetList-CTJZCXDZ.js:1
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
JT @ index-BoOgBszF.js:14
handleClick @ index-BoOgBszF.js:23
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
n @ index-BoOgBszF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BoOgBszF.js:20
xhr @ index-BoOgBszF.js:20
Xg @ index-BoOgBszF.js:22
Promise.then
_request @ index-BoOgBszF.js:23
request @ index-BoOgBszF.js:22
Ga.<computed> @ index-BoOgBszF.js:23
(匿名) @ index-BoOgBszF.js:18
bte @ index-BoOgBszF.js:63
X @ AssetDetail-B4AjOxye.js:3
l @ AssetDetail-B4AjOxye.js:3
(匿名) @ AssetDetail-B4AjOxye.js:3
(匿名) @ index-BoOgBszF.js:14
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
t.__weh.t.__weh @ index-BoOgBszF.js:14
A1 @ index-BoOgBszF.js:14
L1 @ index-BoOgBszF.js:14
Promise.then
P1 @ index-BoOgBszF.js:14
gT @ index-BoOgBszF.js:14
n$ @ index-BoOgBszF.js:14
Zv.i.scheduler @ index-BoOgBszF.js:14
d.scheduler @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
Bv @ index-BoOgBszF.js:10
notify @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
set value @ index-BoOgBszF.js:10
M @ index-BoOgBszF.js:63
(匿名) @ index-BoOgBszF.js:63
Promise.then
O @ index-BoOgBszF.js:63
w @ index-BoOgBszF.js:63
E @ AssetList-CTJZCXDZ.js:1
onClick @ AssetList-CTJZCXDZ.js:1
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
JT @ index-BoOgBszF.js:14
handleClick @ index-BoOgBszF.js:23
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
n @ index-BoOgBszF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BoOgBszF.js:20
xhr @ index-BoOgBszF.js:20
Xg @ index-BoOgBszF.js:22
Promise.then
_request @ index-BoOgBszF.js:23
request @ index-BoOgBszF.js:22
Ga.<computed> @ index-BoOgBszF.js:23
(匿名) @ index-BoOgBszF.js:18
hte @ index-BoOgBszF.js:63
X @ AssetDetail-B4AjOxye.js:3
l @ AssetDetail-B4AjOxye.js:3
(匿名) @ AssetDetail-B4AjOxye.js:3
(匿名) @ index-BoOgBszF.js:14
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
t.__weh.t.__weh @ index-BoOgBszF.js:14
A1 @ index-BoOgBszF.js:14
L1 @ index-BoOgBszF.js:14
Promise.then
P1 @ index-BoOgBszF.js:14
gT @ index-BoOgBszF.js:14
n$ @ index-BoOgBszF.js:14
Zv.i.scheduler @ index-BoOgBszF.js:14
d.scheduler @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
Bv @ index-BoOgBszF.js:10
notify @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
set value @ index-BoOgBszF.js:10
M @ index-BoOgBszF.js:63
(匿名) @ index-BoOgBszF.js:63
Promise.then
O @ index-BoOgBszF.js:63
w @ index-BoOgBszF.js:63
E @ AssetList-CTJZCXDZ.js:1
onClick @ AssetList-CTJZCXDZ.js:1
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
JT @ index-BoOgBszF.js:14
handleClick @ index-BoOgBszF.js:23
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
n @ index-BoOgBszF.js:18
inspector.js:7   GET https://service.bogoo.net/api/assets/1 500 (Internal Server Error)
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BoOgBszF.js:20
xhr @ index-BoOgBszF.js:20
Xg @ index-BoOgBszF.js:22
Promise.then
_request @ index-BoOgBszF.js:23
request @ index-BoOgBszF.js:22
(匿名) @ index-BoOgBszF.js:18
d @ asset-CkmFDqt2.js:1
_ @ useAssetData-cb_OZZLX.js:1
M @ AssetDetail-B4AjOxye.js:3
l @ AssetDetail-B4AjOxye.js:3
await in l
(匿名) @ AssetDetail-B4AjOxye.js:3
(匿名) @ index-BoOgBszF.js:14
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
t.__weh.t.__weh @ index-BoOgBszF.js:14
A1 @ index-BoOgBszF.js:14
L1 @ index-BoOgBszF.js:14
Promise.then
P1 @ index-BoOgBszF.js:14
gT @ index-BoOgBszF.js:14
n$ @ index-BoOgBszF.js:14
Zv.i.scheduler @ index-BoOgBszF.js:14
d.scheduler @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
Bv @ index-BoOgBszF.js:10
notify @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
set value @ index-BoOgBszF.js:10
M @ index-BoOgBszF.js:63
(匿名) @ index-BoOgBszF.js:63
Promise.then
O @ index-BoOgBszF.js:63
w @ index-BoOgBszF.js:63
E @ AssetList-CTJZCXDZ.js:1
onClick @ AssetList-CTJZCXDZ.js:1
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
JT @ index-BoOgBszF.js:14
handleClick @ index-BoOgBszF.js:23
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
n @ index-BoOgBszF.js:18
index-BoOgBszF.js:59  API Error:  xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
(匿名) @ index-BoOgBszF.js:59
Promise.then
_request @ index-BoOgBszF.js:23
request @ index-BoOgBszF.js:22
(匿名) @ index-BoOgBszF.js:18
d @ asset-CkmFDqt2.js:1
_ @ useAssetData-cb_OZZLX.js:1
M @ AssetDetail-B4AjOxye.js:3
l @ AssetDetail-B4AjOxye.js:3
await in l
(匿名) @ AssetDetail-B4AjOxye.js:3
(匿名) @ index-BoOgBszF.js:14
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
t.__weh.t.__weh @ index-BoOgBszF.js:14
A1 @ index-BoOgBszF.js:14
L1 @ index-BoOgBszF.js:14
Promise.then
P1 @ index-BoOgBszF.js:14
gT @ index-BoOgBszF.js:14
n$ @ index-BoOgBszF.js:14
Zv.i.scheduler @ index-BoOgBszF.js:14
d.scheduler @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
Bv @ index-BoOgBszF.js:10
notify @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
set value @ index-BoOgBszF.js:10
M @ index-BoOgBszF.js:63
(匿名) @ index-BoOgBszF.js:63
Promise.then
O @ index-BoOgBszF.js:63
w @ index-BoOgBszF.js:63
E @ AssetList-CTJZCXDZ.js:1
onClick @ AssetList-CTJZCXDZ.js:1
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
JT @ index-BoOgBszF.js:14
handleClick @ index-BoOgBszF.js:23
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
n @ index-BoOgBszF.js:18
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
AssetDetail-B4AjOxye.js:3  Uncaught (in promise) xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
n2 @ index-BoOgBszF.js:20
g @ index-BoOgBszF.js:20
XMLHttpRequest.send
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BoOgBszF.js:20
xhr @ index-BoOgBszF.js:20
Xg @ index-BoOgBszF.js:22
Promise.then
_request @ index-BoOgBszF.js:23
request @ index-BoOgBszF.js:22
(匿名) @ index-BoOgBszF.js:18
d @ asset-CkmFDqt2.js:1
_ @ useAssetData-cb_OZZLX.js:1
M @ AssetDetail-B4AjOxye.js:3
l @ AssetDetail-B4AjOxye.js:3
await in l
(匿名) @ AssetDetail-B4AjOxye.js:3
(匿名) @ index-BoOgBszF.js:14
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
t.__weh.t.__weh @ index-BoOgBszF.js:14
A1 @ index-BoOgBszF.js:14
L1 @ index-BoOgBszF.js:14
Promise.then
P1 @ index-BoOgBszF.js:14
gT @ index-BoOgBszF.js:14
n$ @ index-BoOgBszF.js:14
Zv.i.scheduler @ index-BoOgBszF.js:14
d.scheduler @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
Bv @ index-BoOgBszF.js:10
notify @ index-BoOgBszF.js:10
trigger @ index-BoOgBszF.js:10
set value @ index-BoOgBszF.js:10
M @ index-BoOgBszF.js:63
(匿名) @ index-BoOgBszF.js:63
Promise.then
O @ index-BoOgBszF.js:63
w @ index-BoOgBszF.js:63
E @ AssetList-CTJZCXDZ.js:1
onClick @ AssetList-CTJZCXDZ.js:1
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
JT @ index-BoOgBszF.js:14
handleClick @ index-BoOgBszF.js:23
Yi @ index-BoOgBszF.js:14
Wo @ index-BoOgBszF.js:14
n @ index-BoOgBszF.js:18
inspector.js:7 XHR 加载失败:GET "https://service.bogoo.net/api/assets/1".



root@iZ7xvczjzdx1cgmypn8ufmZ:~# pm2 logs customer-backend --lines 50
[TAILING] Tailing last 50 lines for [customer-backend] process (change the value with --lines option)
/var/www/customer_system/backend/logs/customer-backend-out.log last 50 lines:
4|customer | 2025-07-28 13:40:05 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 13:40:05 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 13:40:05 +08:00: }
4|customer | 2025-07-28 13:40:05 +08:00: 产品订单表体数据: { product_id: 3, order_id: 16, user_count: 3, account_count: 5 }
4|customer | 2025-07-28 13:51:23 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 13:51:23 +08:00:   user_id: 1,
4|customer | 2025-07-28 13:51:23 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 13:51:23 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 13:51:23 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 13:51:23 +08:00: }
4|customer | 2025-07-28 13:51:23 +08:00: 产品订单表体数据: { product_id: 3, order_id: 17, user_count: 3, account_count: 5 }
4|customer | 2025-07-28 14:06:30 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 14:06:30 +08:00:   user_id: 1,
4|customer | 2025-07-28 14:06:30 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 14:06:30 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 14:06:30 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 14:06:30 +08:00: }
4|customer | 2025-07-28 14:06:30 +08:00: 产品订单表体数据: { product_id: 3, order_id: 18, user_count: 3, account_count: 7 }
4|customer | 2025-07-28 14:11:19 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-28 14:11:19 +08:00: 数据库连接已关闭
4|customer | 2025-07-28 14:11:19 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-28 14:11:19 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-28 14:12:49 +08:00: 服务订单数据清理后: {
4|customer | 2025-07-28 14:12:49 +08:00:   user_id: 1,
4|customer | 2025-07-28 14:12:49 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 14:12:49 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 14:12:49 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 14:12:49 +08:00: }
4|customer | 2025-07-28 14:25:16 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-28 14:25:16 +08:00: 数据库连接已关闭
4|customer | 2025-07-28 14:25:17 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-28 14:25:17 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-28 14:26:01 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 14:26:01 +08:00:   user_id: 1,
4|customer | 2025-07-28 14:26:01 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 14:26:01 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 14:26:01 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 14:26:01 +08:00: }
4|customer | 2025-07-28 14:26:01 +08:00: 产品订单表体数据: { product_id: 3, order_id: 20, user_count: 3, account_count: 6 }
4|customer | 2025-07-28 14:26:39 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 14:26:39 +08:00:   user_id: 1,
4|customer | 2025-07-28 14:26:39 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 14:26:39 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 14:26:39 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 14:26:39 +08:00: }
4|customer | 2025-07-28 14:26:39 +08:00: 产品订单表体数据: { product_id: 3, order_id: 21, user_count: 3, account_count: 7 }
4|customer | 2025-07-28 14:38:45 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-28 14:38:45 +08:00: 数据库连接已关闭
4|customer | 2025-07-28 14:38:46 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-28 14:38:46 +08:00: 服务器正在端口 3002 上运行.

/var/www/customer_system/backend/logs/customer-backend-error.log last 50 lines:
4|customer | e_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_category` AS `orders.order_category`, `orders`.`order_type` AS `orders.order_type`, `orders`.`actual_amount` AS `orders.actual_amount`, `orders`.`payment_status` AS `orders.payment_status`, `orders`.`audit_status` AS `orders.audit_status`, `orders`.`createdAt` AS `orders.createdAt`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer | 2025-07-28 14:39:46 +08:00:     parameters: undefined
4|customer | 2025-07-28 14:39:46 +08:00:   },
4|customer | 2025-07-28 14:39:46 +08:00:   sql: "SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_ratio` AS `user.commission_ratio`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `product->features`.`id` AS `product.features.id`, `product->features`.`feature_id` AS `product.features.feature_id`, `product->features`.`feature_name` AS `product.features.feature_name`, `product->features`.`description` AS `product.features.description`, `product->features->ProductFeatureRelation`.`id` AS `product.features.ProductFeatureRelation.id`, `product->features->ProductFeatureRelation`.`feature_price` AS `product.features.ProductFeatureRelation.feature_price`, `product->features->ProductFeatureRelation`.`remark` AS `product.features.ProductFeatureRelation.remark`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark`, `changeLogs`.`id` AS `changeLogs.id`, `changeLogs`.`asset_change_id` AS `changeLogs.asset_change_id`, `changeLogs`.`change_date` AS `changeLogs.change_date`, `changeLogs`.`asset_id` AS `changeLogs.asset_id`, `changeLogs`.`snapshot_before` AS `changeLogs.snapshot_before`, `changeLogs`.`snapshot_after` AS `changeLogs.snapshot_after`, `changeLogs`.`remark` AS `changeLogs.remark`, `changeLogs`.`creator_id` AS `changeLogs.creator_id`, `changeLogs`.`createdAt` AS `changeLogs.createdAt`, `changeLogs`.`updatedAt` AS `changeLogs.updatedAt`, `changeLogs->creator`.`id` AS `changeLogs.creator.id`, `changeLogs->creator`.`employee_number` AS `changeLogs.creator.employee_number`, `changeLogs->creator`.`name` AS `changeLogs.creator.name`, `changeLogs->creator`.`mobile` AS `changeLogs.creator.mobile`, `changeLogs->creator`.`department` AS `changeLogs.creator.department`, `changeLogs->creator`.`password` AS `changeLogs.creator.password`, `changeLogs->creator`.`role` AS `changeLogs.creator.role`, `changeLogs->creator`.`remark` AS `changeLogs.creator.remark`, `orders`.`id` AS `orders.id`, `orders`.`order_id` AS `orders.order_id`, `orders`.`order_category` AS `orders.order_category`, `orders`.`order_type` AS `orders.order_type`, `orders`.`actual_amount` AS `orders.actual_amount`, `orders`.`payment_status` AS `orders.payment_status`, `orders`.`audit_status` AS `orders.audit_status`, `orders`.`createdAt` AS `orders.createdAt`, `orders`.`remark` AS `orders.remark`, `orders->creator`.`id` AS `orders.creator.id`, `orders->creator`.`name` AS `orders.creator.name` FROM `asset` AS `Asset` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN ( `product_feature_relation` AS `product->features->ProductFeatureRelation` INNER JOIN `product_feature` AS `product->features` ON `product->features`.`id` = `product->features->ProductFeatureRelation`.`product_feature_id`) ON `product`.`id` = `product->features->ProductFeatureRelation`.`product_id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id` LEFT OUTER JOIN `asset_change_log` AS `changeLogs` ON `Asset`.`id` = `changeLogs`.`asset_id` LEFT OUTER JOIN `employee` AS `changeLogs->creator` ON `changeLogs`.`creator_id` = `changeLogs->creator`.`id` LEFT OUTER JOIN `order_head` AS `orders` ON `Asset`.`id` = `orders`.`asset_id` LEFT OUTER JOIN `employee` AS `orders->creator` ON `orders`.`creator_id` = `orders->creator`.`id` WHERE `Asset`.`id` = '1' ORDER BY `changeLogs`.`change_date` DESC;",
4|customer | 2025-07-28 14:39:46 +08:00:   parameters: {}
4|customer | 2025-07-28 14:39:46 +08:00: }
