// /frontend/src/api/order.js
import service from '@/utils/request_extra.js';

const API_PATH = '/orders';

/**
 * [新增] 获取下一个可用的订单ID
 * @param {string} orderType - 订单类型 'product' 或 'service'
 */
export const getNextOrderId = (orderType = 'product') => {
  return service.get(`${API_PATH}/next-id`, { params: { orderType } });
};

// === 订单审核相关 API ===

/**
 * 获取订单审核列表 - 显示所有待审核的订单
 */
export const getOrdersForReview = () => {
  return service.get(`${API_PATH}/review`);
};

/**
 * 审核订单 - 通过审核
 * @param {number} id - 订单主键ID
 */
export const approveOrder = (id) => {
  return service.post(`${API_PATH}/${id}/approve`);
};

/**
 * 审核订单 - 拒绝审核
 * @param {number} id - 订单主键ID
 * @param {string} reason - 拒绝原因
 */
export const rejectOrder = (id, reason) => {
  return service.post(`${API_PATH}/${id}/reject`, { reason });
};

// === 产品订单相关 API ===

/**
 * 获取产品订单列表 - 显示已审核通过的产品订单
 */
export const getProductOrders = () => {
  return service.get(`${API_PATH}/product`);
};

/**
 * 创建产品订单
 * @param {object} orderData - 产品订单数据
 */
export const createProductOrder = (orderData) => {
  return service.post(`${API_PATH}/product`, orderData);
};

// === 服务订单相关 API ===

/**
 * 获取服务订单列表 - 显示已审核通过的服务订单
 */
export const getServiceOrders = () => {
  return service.get(`${API_PATH}/service`);
};

/**
 * 创建服务订单
 * @param {object} orderData - 服务订单数据
 */
export const createServiceOrder = (orderData) => {
  return service.post(`${API_PATH}/service`, orderData);
};

// === 通用订单操作 API ===

/**
 * 根据ID获取订单详情
 * @param {number} id - 订单主键ID
 */
export const getOrderById = (id) => {
  return service.get(`${API_PATH}/${id}`);
};

/**
 * 更新订单
 * @param {number} id - 订单主键ID
 * @param {object} orderData - 订单数据
 */
export const updateOrder = (id, orderData) => {
  return service.put(`${API_PATH}/${id}`, orderData);
};

/**
 * 删除订单
 * @param {number} id - 订单主键ID
 */
export const deleteOrder = (id) => {
  return service.delete(`${API_PATH}/${id}`);
};

// === 订单附件相关 API ===

/**
 * 获取订单附件列表
 * @param {number} orderId - 订单ID
 */
export const getAttachments = (orderId) => {
  return service.get(`${API_PATH}/${orderId}/attachments`);
};

/**
 * 删除订单附件
 * @param {number} orderId - 订单ID
 * @param {number} attachmentId - 附件ID
 */
export const deleteAttachment = (orderId, attachmentId) => {
  return service.delete(`${API_PATH}/${orderId}/attachments/${attachmentId}`);
};

// === 其他订单相关 API ===

/**
 * 调用后端接口计算订单项的预览价格
 * @param {object} priceRequest - 包含计算所需数据的对象
 * @returns Promise
 */
export const calculatePrice = (priceRequest) => {
  return service.post(`${API_PATH}/pricing/calculate`, priceRequest);
};

/**
 * 获取订单列表（通用）
 * @param {object} params - 查询参数 (e.g., { asset_id: 1, enterprise_id: 2, linked: false })
 */
export const getOrders = (params = {}) => {
  return service.get(API_PATH, { params });
};

/**
 * 根据企业ID获取订单列表
 * @param {string|number} enterpriseId - 企业ID
 * @param {object} params - 其他查询参数 (e.g., { unbound: true })
 */
export const getOrdersByEnterprise = (enterpriseId, params = {}) => {
  return service.get(`/enterprises/${enterpriseId}/orders`, { params });
};

/**
 * 创建一个新订单（通用）
 * @param {object} orderData - 包含订单主信息和 'items' 数组
 */
export const createOrder = (orderData) => {
  return service.post(API_PATH, orderData);
};

/**
 * 获取订单的操作日志
 * @param {string} id - 订单ID
 */
export const getOrderLogs = (id) => {
  return service.get(`${API_PATH}/${id}/logs`);
};

// === 订单明细相关 API ===

/**
 * 为指定订单添加一个新明细
 * @param {string} orderId - 订单ID
 * @param {object} itemData - 新明细的数据
 */
export const addOrderItem = (orderId, itemData) => {
  return service.post(`${API_PATH}/${orderId}/items`, itemData);
};

/**
 * 更新订单的某个明细
 * @param {string} orderId - 订单ID
 * @param {string|number} itemId - 明细ID
 * @param {object} itemData - 要更新的明细数据
 */
export const updateOrderItem = (orderId, itemId, itemData) => {
  return service.put(`${API_PATH}/${orderId}/items/${itemId}`, itemData);
};

/**
 * 删除订单的某个明细
 * @param {string} orderId - 订单ID
 * @param {string|number} itemId - 明细ID
 */
export const deleteOrderItem = (orderId, itemId) => {
  return service.delete(`${API_PATH}/${orderId}/items/${itemId}`);
};

/**
 * 为指定订单上传附件
 * @param {string} orderId 订单ID
 * @param {FormData} formData 包含文件的表单数据
 */
export const uploadAttachment = (orderId, formData) => {
  return service.post(`${API_PATH}/${orderId}/attachments`, formData);
};