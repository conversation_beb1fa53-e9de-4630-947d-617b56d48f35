#!/bin/bash

echo "=== 测试订单附件上传功能 ==="
echo "修复内容："
echo "1. 添加了文件类型选择功能（合同、发票、其他）"
echo "2. 添加了备注输入功能"
echo "3. 修复了上传认证问题"
echo

echo "重启前端和后端服务..."
pm2 restart customer-backend
cd frontend && npm run build
cd ..

echo
echo "等待服务启动..."
sleep 5

echo "检查服务状态..."
pm2 list | grep customer

echo
echo "=== 测试完成 ==="
echo "现在可以测试以下功能："
echo "1. 在订单页面上传附件时可以选择文件类型"
echo "2. 可以添加备注信息"
echo "3. 上传成功后表单会自动清空"
echo "4. 数据库中应该正确保存文件类型和备注"
