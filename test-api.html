<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
</head>
<body>
    <h1>API连接测试</h1>
    <div id="results"></div>
    
    <script>
        const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MiwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJlbXBsb3llZSIsImlhdCI6MTczMjc3NzE5MSwiZXhwIjoxNzMyODYzNTkxfQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
        const resultsDiv = document.getElementById('results');
        
        async function testAPI() {
            try {
                // 测试GET请求
                console.log('测试GET请求...');
                const getResponse = await fetch('https://service.bogoo.net/api/enterprises', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (getResponse.ok) {
                    const data = await getResponse.json();
                    resultsDiv.innerHTML += `<p>✅ GET请求成功: 获取到 ${data.length} 条企业记录</p>`;
                } else {
                    resultsDiv.innerHTML += `<p>❌ GET请求失败: ${getResponse.status} ${getResponse.statusText}</p>`;
                }
                
                // 测试PUT请求
                console.log('测试PUT请求...');
                const putResponse = await fetch('https://service.bogoo.net/api/enterprises/9', {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: '测试企业更新',
                        contact_person: '测试联系人'
                    })
                });
                
                if (putResponse.ok) {
                    const data = await putResponse.json();
                    resultsDiv.innerHTML += `<p>✅ PUT请求成功: 企业信息已更新</p>`;
                } else {
                    resultsDiv.innerHTML += `<p>❌ PUT请求失败: ${putResponse.status} ${putResponse.statusText}</p>`;
                }
                
                // 测试POST请求
                console.log('测试POST请求...');
                const postResponse = await fetch('https://service.bogoo.net/api/followups', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        enterprise_id: 9,
                        followup_time: new Date().toISOString(),
                        situation: '测试跟进记录',
                        employee_id: 2,
                        remark: '这是一个测试记录'
                    })
                });
                
                if (postResponse.ok) {
                    const data = await postResponse.json();
                    resultsDiv.innerHTML += `<p>✅ POST请求成功: 跟进记录已创建</p>`;
                } else {
                    resultsDiv.innerHTML += `<p>❌ POST请求失败: ${postResponse.status} ${postResponse.statusText}</p>`;
                }
                
            } catch (error) {
                resultsDiv.innerHTML += `<p>❌ 请求异常: ${error.message}</p>`;
                console.error('API测试错误:', error);
            }
        }
        
        // 页面加载后自动测试
        window.onload = testAPI;
    </script>
</body>
</html>
