监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ProductOrderForm-cO49gqDQ.js:1 初始化新产品订单
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ product-EgvQ-d_C.js:1
Y @ ProductOrderForm-cO49gqDQ.js:1
(匿名) @ ProductOrderForm-cO49gqDQ.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
install @ index-CVl3lHbF.js:63
use @ index-CVl3lHbF.js:14
(匿名) @ index-CVl3lHbF.js:63
ReviewActions-Dy-ctK2I.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: null, user_id: null, formData: Proxy(Object)}
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: 'PO20250728TYD', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: 'PO20250728TYD', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: 'PO20250728TYD', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/next-id?orderType=product"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
c @ order-DWor6qrj.js:1
I @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
install @ index-CVl3lHbF.js:63
use @ index-CVl3lHbF.js:14
(匿名) @ index-CVl3lHbF.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:18
o @ asset-BiPl0xug.js:1
S @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
install @ index-CVl3lHbF.js:63
use @ index-CVl3lHbF.js:14
(匿名) @ index-CVl3lHbF.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
bte @ index-CVl3lHbF.js:63
O @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
install @ index-CVl3lHbF.js:63
use @ index-CVl3lHbF.js:14
(匿名) @ index-CVl3lHbF.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
hte @ index-CVl3lHbF.js:63
$ @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
install @ index-CVl3lHbF.js:63
use @ index-CVl3lHbF.js:14
(匿名) @ index-CVl3lHbF.js:63
ReviewActions-Dy-ctK2I.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: null, user_id: null, formData: Proxy(Object)}
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-Dy-ctK2I.js:1 监听到 composable 选择变化: {enterpriseId: 9, userId: 1, assetId: 1, currentFormData: {…}}
ReviewActions-Dy-ctK2I.js:1 更新 formData.enterprise_id 为: 9
ReviewActions-Dy-ctK2I.js:1 更新 formData.user_id 为: 1
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: 'PO20250728TYD', order_category: '产品订单', enterprise_id: null, asset_id: 1, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: 'PO20250728TYD', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-Dy-ctK2I.js:1 保留当前enterprise_id: 9
ReviewActions-Dy-ctK2I.js:1 保留当前user_id: 1
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: 'PO20250728TYD', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-Dy-ctK2I.js:1 企业字段验证: {value: 9, enterpriseId: 9, userId: 1, enterpriseType: 'number', userType: 'number'}
ReviewActions-Dy-ctK2I.js:1 用户字段验证: {value: 1, userId: 1, enterpriseId: 9, userType: 'number', enterpriseType: 'number'}
ReviewActions-Dy-ctK2I.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 9, user_id: 1, formData: Proxy(Object)}
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 9, user_id: 1, enterprise_type: 'number', user_type: 'number'}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: 'PO20250728TYD', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: 'PO20250728TYD', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: 'PO20250728TYD', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products/3/features"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
a @ product-EgvQ-d_C.js:1
A @ product-EgvQ-d_C.js:1
Z @ ProductOrderForm-cO49gqDQ.js:1
K @ ProductOrderForm-cO49gqDQ.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
mt @ index-CVl3lHbF.js:40
ye @ index-CVl3lHbF.js:40
m @ index-CVl3lHbF.js:40
Ge.n.<computed>.n.<computed> @ index-CVl3lHbF.js:18
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ProductOrderForm-cO49gqDQ.js:1 保存前的完整订单数据: {user_id: 1, enterprise_id: 9, user_id_type: 'number', enterprise_id_type: 'number', orderData: Proxy(Object)}
ReviewActions-Dy-ctK2I.js:1 OrderHeader 验证前的数据: {enterprise_id: 9, user_id: 1, enterprise_id_type: 'number', user_id_type: 'number', selectedEnterprise: 9, …}
ReviewActions-Dy-ctK2I.js:1 企业字段验证: {value: 9, enterpriseId: 9, userId: 1, enterpriseType: 'number', userType: 'number'}
ReviewActions-Dy-ctK2I.js:1 用户字段验证: {value: 1, userId: 1, enterpriseId: 9, userType: 'number', enterpriseType: 'number'}
ProductOrderForm-cO49gqDQ.js:1 准备创建产品订单，数据: {user_id: 1, enterprise_id: 9, productItem: Proxy(Object), standard_amount: 0, actual_amount: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 3, user_count: 3, account_count: 5, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {product_id: 3, user_count: 3, account_count: 5, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: 1998, actual_amount: 1998}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 1998, actualPrice: 1998}
ProductOrderForm-cO49gqDQ.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 3, user_count: 3, account_count: 5, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {product_id: 3, user_count: 3, account_count: 5, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: 1998, actual_amount: 1998}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 1998, actualPrice: 1998}
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/pricing/calculate"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
se @ ProductOrderForm-cO49gqDQ.js:1
E @ ProductOrderForm-cO49gqDQ.js:1
K @ ProductOrderForm-cO49gqDQ.js:1
await in K
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
mt @ index-CVl3lHbF.js:40
ye @ index-CVl3lHbF.js:40
m @ index-CVl3lHbF.js:40
Ge.n.<computed>.n.<computed> @ index-CVl3lHbF.js:18
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7   POST https://service.bogoo.net/api/orders/product 500 (Internal Server Error)
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ order-DWor6qrj.js:1
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
index-CVl3lHbF.js:59  API Error:  xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
(匿名) @ index-CVl3lHbF.js:59
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ order-DWor6qrj.js:1
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ProductOrderForm-cO49gqDQ.js:1  保存失败: xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 加载失败:POST "https://service.bogoo.net/api/orders/product".
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ order-DWor6qrj.js:1
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ProductOrderForm-cO49gqDQ.js:1 保存前的完整订单数据: {user_id: 1, enterprise_id: 9, user_id_type: 'number', enterprise_id_type: 'number', orderData: Proxy(Object)}
ReviewActions-Dy-ctK2I.js:1 OrderHeader 验证前的数据: {enterprise_id: 9, user_id: 1, enterprise_id_type: 'number', user_id_type: 'number', selectedEnterprise: 9, …}
ReviewActions-Dy-ctK2I.js:1 企业字段验证: {value: 9, enterpriseId: 9, userId: 1, enterpriseType: 'number', userType: 'number'}
ReviewActions-Dy-ctK2I.js:1 用户字段验证: {value: 1, userId: 1, enterpriseId: 9, userType: 'number', enterpriseType: 'number'}
ProductOrderForm-cO49gqDQ.js:1 准备创建产品订单，数据: {user_id: 1, enterprise_id: 9, productItem: Proxy(Object), standard_amount: 1998, actual_amount: 1998}
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/orders/product"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ order-DWor6qrj.js:1
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/19/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
m @ order-DWor6qrj.js:1
O @ ReviewActions-Dy-ctK2I.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
Zv.i.call @ index-CVl3lHbF.js:14
b @ index-CVl3lHbF.js:10
vT @ index-CVl3lHbF.js:10
Zv @ index-CVl3lHbF.js:14
fe @ index-CVl3lHbF.js:14
setup @ ReviewActions-Dy-ctK2I.js:1
Yi @ index-CVl3lHbF.js:14
u$ @ index-CVl3lHbF.js:14
i$ @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
J @ index-CVl3lHbF.js:14
I @ index-CVl3lHbF.js:14
M @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
x @ index-CVl3lHbF.js:14
T @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
runIfDirty @ index-CVl3lHbF.js:10
Yi @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
jv @ index-CVl3lHbF.js:14
P.$e.scheduler @ index-CVl3lHbF.js:14
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/19/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
m @ order-DWor6qrj.js:1
O @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
jv @ index-CVl3lHbF.js:14
P.$e.scheduler @ index-CVl3lHbF.js:14
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ServiceOrderForm-CMeBMclT.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ServiceOrderForm-CMeBMclT.js:1 服务明细数据: Proxy(Array) {}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {service_items: Proxy(Array), standard_amount: 0, actual_amount: 0}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ServiceOrderForm-CMeBMclT.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {service_items: Proxy(Array), standard_amount: 0, actual_amount: 0}
ServiceOrderForm-CMeBMclT.js:1 服务明细数据: Proxy(Array) {0: {…}}
ServiceOrderForm-CMeBMclT.js:1 初始化新服务订单
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ProductOrderForm-cO49gqDQ.js:1 初始化新产品订单
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/next-id?orderType=service"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
c @ order-DWor6qrj.js:1
I @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ product-EgvQ-d_C.js:1
Y @ ProductOrderForm-cO49gqDQ.js:1
(匿名) @ ProductOrderForm-cO49gqDQ.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ReviewActions-Dy-ctK2I.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: null, user_id: null, formData: Proxy(Object)}
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: 'PO202507282LB', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: 'PO202507282LB', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: 'PO202507282LB', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/next-id?orderType=product"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
c @ order-DWor6qrj.js:1
I @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
bte @ index-CVl3lHbF.js:63
O @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:18
o @ asset-BiPl0xug.js:1
S @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
hte @ index-CVl3lHbF.js:63
$ @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:18
o @ asset-BiPl0xug.js:1
S @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
bte @ index-CVl3lHbF.js:63
O @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
hte @ index-CVl3lHbF.js:63
$ @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ReviewActions-Dy-ctK2I.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: null, user_id: null, formData: Proxy(Object)}
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-Dy-ctK2I.js:1 监听到 composable 选择变化: {enterpriseId: 9, userId: 1, assetId: 1, currentFormData: {…}}
ReviewActions-Dy-ctK2I.js:1 更新 formData.enterprise_id 为: 9
ReviewActions-Dy-ctK2I.js:1 更新 formData.user_id 为: 1
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: 'PO202507282LB', order_category: '产品订单', enterprise_id: null, asset_id: 1, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: 'PO202507282LB', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-Dy-ctK2I.js:1 保留当前enterprise_id: 9
ReviewActions-Dy-ctK2I.js:1 保留当前user_id: 1
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: 'PO202507282LB', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-Dy-ctK2I.js:1 企业字段验证: {value: 9, enterpriseId: 9, userId: 1, enterpriseType: 'number', userType: 'number'}
ReviewActions-Dy-ctK2I.js:1 用户字段验证: {value: 1, userId: 1, enterpriseId: 9, userType: 'number', enterpriseType: 'number'}
ReviewActions-Dy-ctK2I.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 9, user_id: 1, formData: Proxy(Object)}
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 9, user_id: 1, enterprise_type: 'number', user_type: 'number'}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: 'PO202507282LB', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: 'PO202507282LB', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: 'PO202507282LB', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products/9/features"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
a @ product-EgvQ-d_C.js:1
A @ product-EgvQ-d_C.js:1
Z @ ProductOrderForm-cO49gqDQ.js:1
K @ ProductOrderForm-cO49gqDQ.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
mt @ index-CVl3lHbF.js:40
ye @ index-CVl3lHbF.js:40
m @ index-CVl3lHbF.js:40
Ge.n.<computed>.n.<computed> @ index-CVl3lHbF.js:18
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ProductOrderForm-cO49gqDQ.js:1 保存前的完整订单数据: {user_id: 1, enterprise_id: 9, user_id_type: 'number', enterprise_id_type: 'number', orderData: Proxy(Object)}
ReviewActions-Dy-ctK2I.js:1 OrderHeader 验证前的数据: {enterprise_id: 9, user_id: 1, enterprise_id_type: 'number', user_id_type: 'number', selectedEnterprise: 9, …}
ReviewActions-Dy-ctK2I.js:1 企业字段验证: {value: 9, enterpriseId: 9, userId: 1, enterpriseType: 'number', userType: 'number'}
ReviewActions-Dy-ctK2I.js:1 用户字段验证: {value: 1, userId: 1, enterpriseId: 9, userType: 'number', enterpriseType: 'number'}
ProductOrderForm-cO49gqDQ.js:1 准备创建产品订单，数据: {user_id: 1, enterprise_id: 9, productItem: Proxy(Object), standard_amount: 0, actual_amount: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 9, user_count: 1, account_count: 3, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {product_id: 9, user_count: 1, account_count: 3, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: 898, actual_amount: 898}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 898, actualPrice: 898}
ProductOrderForm-cO49gqDQ.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 9, user_count: 1, account_count: 3, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {product_id: 9, user_count: 1, account_count: 3, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: 898, actual_amount: 898}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 898, actualPrice: 898}
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/pricing/calculate"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
se @ ProductOrderForm-cO49gqDQ.js:1
E @ ProductOrderForm-cO49gqDQ.js:1
K @ ProductOrderForm-cO49gqDQ.js:1
await in K
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
mt @ index-CVl3lHbF.js:40
ye @ index-CVl3lHbF.js:40
m @ index-CVl3lHbF.js:40
Ge.n.<computed>.n.<computed> @ index-CVl3lHbF.js:18
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7   POST https://service.bogoo.net/api/orders/product 500 (Internal Server Error)
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ order-DWor6qrj.js:1
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
index-CVl3lHbF.js:59  API Error:  xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
(匿名) @ index-CVl3lHbF.js:59
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ order-DWor6qrj.js:1
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ProductOrderForm-cO49gqDQ.js:1  保存失败: xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 加载失败:POST "https://service.bogoo.net/api/orders/product".
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ order-DWor6qrj.js:1
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ProductOrderForm-cO49gqDQ.js:1 保存前的完整订单数据: {user_id: 1, enterprise_id: 9, user_id_type: 'number', enterprise_id_type: 'number', orderData: Proxy(Object)}
ReviewActions-Dy-ctK2I.js:1 OrderHeader 验证前的数据: {enterprise_id: 9, user_id: 1, enterprise_id_type: 'number', user_id_type: 'number', selectedEnterprise: 9, …}
ReviewActions-Dy-ctK2I.js:1 企业字段验证: {value: 9, enterpriseId: 9, userId: 1, enterpriseType: 'number', userType: 'number'}
ReviewActions-Dy-ctK2I.js:1 用户字段验证: {value: 1, userId: 1, enterpriseId: 9, userType: 'number', enterpriseType: 'number'}
ProductOrderForm-cO49gqDQ.js:1 准备创建产品订单，数据: {user_id: 1, enterprise_id: 9, productItem: Proxy(Object), standard_amount: 898, actual_amount: 898}
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/orders/product"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ order-DWor6qrj.js:1
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
m @ order-DWor6qrj.js:1
O @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
jv @ index-CVl3lHbF.js:14
P.$e.scheduler @ index-CVl3lHbF.js:14
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
m @ order-DWor6qrj.js:1
O @ ReviewActions-Dy-ctK2I.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
Zv.i.call @ index-CVl3lHbF.js:14
b @ index-CVl3lHbF.js:10
vT @ index-CVl3lHbF.js:10
Zv @ index-CVl3lHbF.js:14
fe @ index-CVl3lHbF.js:14
setup @ ReviewActions-Dy-ctK2I.js:1
Yi @ index-CVl3lHbF.js:14
u$ @ index-CVl3lHbF.js:14
i$ @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
J @ index-CVl3lHbF.js:14
I @ index-CVl3lHbF.js:14
M @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
x @ index-CVl3lHbF.js:14
T @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
runIfDirty @ index-CVl3lHbF.js:10
Yi @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
jv @ index-CVl3lHbF.js:14
P.$e.scheduler @ index-CVl3lHbF.js:14
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
Z @ ProductOrderForm-cO49gqDQ.js:1
await in Z
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/review"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
o @ order-DWor6qrj.js:1
y @ OrderReviewList-CSxUVyc7.js:1
(匿名) @ OrderReviewList-CSxUVyc7.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
m @ order-DWor6qrj.js:1
O @ ReviewActions-Dy-ctK2I.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
Zv.i.call @ index-CVl3lHbF.js:14
b @ index-CVl3lHbF.js:10
vT @ index-CVl3lHbF.js:10
Zv @ index-CVl3lHbF.js:14
fe @ index-CVl3lHbF.js:14
setup @ ReviewActions-Dy-ctK2I.js:1
Yi @ index-CVl3lHbF.js:14
u$ @ index-CVl3lHbF.js:14
i$ @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
M @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
P @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
M @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
P @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
P @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
runIfDirty @ index-CVl3lHbF.js:10
Yi @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ product-EgvQ-d_C.js:1
Y @ ProductOrderForm-cO49gqDQ.js:1
(匿名) @ ProductOrderForm-cO49gqDQ.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
m @ order-DWor6qrj.js:1
O @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
bte @ index-CVl3lHbF.js:63
O @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {id: 7, order_id: 21, product_id: 9, user_count: 1, account_count: 3, …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: '898.00', actual_amount: '898.00'}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: '898.00', actualPrice: '898.00'}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 9, user_id: 1, enterprise_type: 'number', user_type: 'number'}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: 'PO202507282LB', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: 'PO202507282LB', order_category: '产品订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-Dy-ctK2I.js:1 监听到 composable 选择变化: {enterpriseId: 9, userId: 1, assetId: 1, currentFormData: {…}}
ReviewActions-Dy-ctK2I.js:1 企业字段验证: {value: 9, enterpriseId: 9, userId: 1, enterpriseType: 'number', userType: 'number'}
ReviewActions-Dy-ctK2I.js:1 用户字段验证: {value: 1, userId: 1, enterpriseId: 9, userType: 'number', enterpriseType: 'number'}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/21"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
i @ order-DWor6qrj.js:1
I @ ProductOrderForm-cO49gqDQ.js:1
(匿名) @ ProductOrderForm-cO49gqDQ.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:18
o @ asset-BiPl0xug.js:1
S @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
hte @ index-CVl3lHbF.js:63
$ @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7   POST https://service.bogoo.net/api/orders/21/attachments 500 (Internal Server Error)
XMLHttpRequest.send @ inspector.js:7
vJ @ index-CVl3lHbF.js:58
d @ index-CVl3lHbF.js:58
await in d
u @ index-CVl3lHbF.js:58
await in u
i @ index-CVl3lHbF.js:58
f @ index-CVl3lHbF.js:58
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
index-CVl3lHbF.js:58  UploadAjaxError: {"message":"上传附件失败","error":"Cannot read properties of undefined (reading 'id')"}
    at Ab (index-CVl3lHbF.js:58:145122)
    at XMLHttpRequest.<anonymous> (index-CVl3lHbF.js:58:145787)
i @ index-CVl3lHbF.js:58
onError @ index-CVl3lHbF.js:58
(匿名) @ index-CVl3lHbF.js:58
XMLHttpRequest.send
XMLHttpRequest.send @ inspector.js:7
vJ @ index-CVl3lHbF.js:58
d @ index-CVl3lHbF.js:58
await in d
u @ index-CVl3lHbF.js:58
await in u
i @ index-CVl3lHbF.js:58
f @ index-CVl3lHbF.js:58
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ReviewActions-Dy-ctK2I.js:1  文件上传失败: UploadAjaxError: {"message":"上传附件失败","error":"Cannot read properties of undefined (reading 'id')"}
    at Ab (index-CVl3lHbF.js:58:145122)
    at XMLHttpRequest.<anonymous> (index-CVl3lHbF.js:58:145787)
G @ ReviewActions-Dy-ctK2I.js:1
i @ index-CVl3lHbF.js:58
onError @ index-CVl3lHbF.js:58
(匿名) @ index-CVl3lHbF.js:58
XMLHttpRequest.send
XMLHttpRequest.send @ inspector.js:7
vJ @ index-CVl3lHbF.js:58
d @ index-CVl3lHbF.js:58
await in d
u @ index-CVl3lHbF.js:58
await in u
i @ index-CVl3lHbF.js:58
f @ index-CVl3lHbF.js:58
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 加载失败:POST "https://service.bogoo.net/api/orders/21/attachments".
XMLHttpRequest.send @ inspector.js:7
vJ @ index-CVl3lHbF.js:58
d @ index-CVl3lHbF.js:58
await in d
u @ index-CVl3lHbF.js:58
await in u
i @ index-CVl3lHbF.js:58
f @ index-CVl3lHbF.js:58
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7   POST https://service.bogoo.net/api/orders/21/attachments 500 (Internal Server Error)
XMLHttpRequest.send @ inspector.js:7
vJ @ index-CVl3lHbF.js:58
d @ index-CVl3lHbF.js:58
await in d
u @ index-CVl3lHbF.js:58
await in u
i @ index-CVl3lHbF.js:58
f @ index-CVl3lHbF.js:58
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
index-CVl3lHbF.js:58  UploadAjaxError: {"message":"上传附件失败","error":"Cannot read properties of undefined (reading 'id')"}
    at Ab (index-CVl3lHbF.js:58:145122)
    at XMLHttpRequest.<anonymous> (index-CVl3lHbF.js:58:145787)
i @ index-CVl3lHbF.js:58
onError @ index-CVl3lHbF.js:58
(匿名) @ index-CVl3lHbF.js:58
XMLHttpRequest.send
XMLHttpRequest.send @ inspector.js:7
vJ @ index-CVl3lHbF.js:58
d @ index-CVl3lHbF.js:58
await in d
u @ index-CVl3lHbF.js:58
await in u
i @ index-CVl3lHbF.js:58
f @ index-CVl3lHbF.js:58
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ReviewActions-Dy-ctK2I.js:1  文件上传失败: UploadAjaxError: {"message":"上传附件失败","error":"Cannot read properties of undefined (reading 'id')"}
    at Ab (index-CVl3lHbF.js:58:145122)
    at XMLHttpRequest.<anonymous> (index-CVl3lHbF.js:58:145787)
G @ ReviewActions-Dy-ctK2I.js:1
i @ index-CVl3lHbF.js:58
onError @ index-CVl3lHbF.js:58
(匿名) @ index-CVl3lHbF.js:58
XMLHttpRequest.send
XMLHttpRequest.send @ inspector.js:7
vJ @ index-CVl3lHbF.js:58
d @ index-CVl3lHbF.js:58
await in d
u @ index-CVl3lHbF.js:58
await in u
i @ index-CVl3lHbF.js:58
f @ index-CVl3lHbF.js:58
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 加载失败:POST "https://service.bogoo.net/api/orders/21/attachments".



root@iZ7xvczjzdx1cgmypn8ufmZ:/var/www/customer_system# pm2 logs customer-backend --lines 50
[TAILING] Tailing last 50 lines for [customer-backend] process (change the value with --lines option)
/var/www/customer_system/backend/logs/customer-backend-out.log last 50 lines:
4|customer | 2025-07-28 10:47:51 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-28 10:47:51 +08:00: 数据库连接已关闭
4|customer | 2025-07-28 10:47:52 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-28 10:47:52 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-28 10:48:55 +08:00: 管理员权限，跳过所有权检查
4|customer | 2025-07-28 10:55:46 +08:00: 收到 SIGINT 信号，开始优雅关闭...
4|customer | 2025-07-28 10:55:46 +08:00: 数据库连接已关闭
4|customer | 2025-07-28 10:55:46 +08:00: 数据库连接成功，且模型关联已加载。
4|customer | 2025-07-28 10:55:46 +08:00: 服务器正在端口 3002 上运行.
4|customer | 2025-07-28 10:56:50 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 10:56:50 +08:00:   user_id: 1,
4|customer | 2025-07-28 10:56:50 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 10:56:50 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 10:56:50 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 10:56:50 +08:00: }
4|customer | 2025-07-28 10:56:59 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 10:56:59 +08:00:   user_id: 1,
4|customer | 2025-07-28 10:56:59 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 10:56:59 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 10:56:59 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 10:56:59 +08:00: }
4|customer | 2025-07-28 10:57:13 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 10:57:13 +08:00:   user_id: 1,
4|customer | 2025-07-28 10:57:13 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 10:57:13 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 10:57:13 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 10:57:13 +08:00: }
4|customer | 2025-07-28 10:57:25 +08:00: 产品订单数据清理后: {
4|customer | 2025-07-28 10:57:25 +08:00:   user_id: 1,
4|customer | 2025-07-28 10:57:25 +08:00:   enterprise_id: 9,
4|customer | 2025-07-28 10:57:25 +08:00:   user_id_type: 'number',
4|customer | 2025-07-28 10:57:25 +08:00:   enterprise_id_type: 'number'
4|customer | 2025-07-28 10:57:25 +08:00: }

/var/www/customer_system/backend/logs/customer-backend-error.log last 50 lines:
4|customer | 2025-07-28 10:56:50 +08:00:       validatorName: null,
4|customer | 2025-07-28 10:56:50 +08:00:       validatorArgs: []
4|customer | 2025-07-28 10:56:50 +08:00:     }
4|customer | 2025-07-28 10:56:50 +08:00:   ]
4|customer | 2025-07-28 10:56:50 +08:00: }
4|customer | 2025-07-28 10:57:13 +08:00: 创建产品订单失败: ValidationError [SequelizeValidationError]: notNull Violation: OrderProductItem.product_id cannot be null
4|customer | 2025-07-28 10:57:13 +08:00:     at InstanceValidator._validate (/var/www/customer_system/backend/node_modules/sequelize/lib/instance-validator.js:50:13)
4|customer | 2025-07-28 10:57:13 +08:00:     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
4|customer | 2025-07-28 10:57:13 +08:00:     at async InstanceValidator._validateAndRunHooks (/var/www/customer_system/backend/node_modules/sequelize/lib/instance-validator.js:60:7)
4|customer | 2025-07-28 10:57:13 +08:00:     at async InstanceValidator.validate (/var/www/customer_system/backend/node_modules/sequelize/lib/instance-validator.js:54:12)
4|customer | 2025-07-28 10:57:13 +08:00:     at async model.save (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:2426:7)
4|customer | 2025-07-28 10:57:13 +08:00:     at async OrderProductItem.create (/var/www/customer_system/backend/node_modules/sequelize/lib/model.js:1362:12)
4|customer | 2025-07-28 10:57:13 +08:00:     at async /var/www/customer_system/backend/src/services/order.service.js:142:13
4|customer | 2025-07-28 10:57:13 +08:00:     at async /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:507:18
4|customer | 2025-07-28 10:57:13 +08:00:     at async exports.createProductOrder (/var/www/customer_system/backend/src/controllers/order.controller.js:82:26) {
4|customer | 2025-07-28 10:57:13 +08:00:   errors: [
4|customer | 2025-07-28 10:57:13 +08:00:     ValidationErrorItem {
4|customer | 2025-07-28 10:57:13 +08:00:       message: 'OrderProductItem.product_id cannot be null',
4|customer | 2025-07-28 10:57:13 +08:00:       type: 'notNull Violation',
4|customer | 2025-07-28 10:57:13 +08:00:       path: 'product_id',
4|customer | 2025-07-28 10:57:13 +08:00:       value: null,
4|customer | 2025-07-28 10:57:13 +08:00:       origin: 'CORE',
4|customer | 2025-07-28 10:57:13 +08:00:       instance: [OrderProductItem],
4|customer | 2025-07-28 10:57:13 +08:00:       validatorKey: 'is_null',
4|customer | 2025-07-28 10:57:13 +08:00:       validatorName: null,
4|customer | 2025-07-28 10:57:13 +08:00:       validatorArgs: []
4|customer | 2025-07-28 10:57:13 +08:00:     }
4|customer | 2025-07-28 10:57:13 +08:00:   ]
4|customer | 2025-07-28 10:57:13 +08:00: }
4|customer | 2025-07-28 10:57:43 +08:00: 上传附件失败: TypeError: Cannot read properties of undefined (reading 'id')
4|customer | 2025-07-28 10:57:43 +08:00:     at exports.addAttachment (/var/www/customer_system/backend/src/controllers/order.controller.js:206:39)
4|customer | 2025-07-28 10:57:43 +08:00:     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
4|customer | 2025-07-28 10:57:43 +08:00:     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
4|customer | 2025-07-28 10:57:43 +08:00:     at done (/var/www/customer_system/backend/node_modules/multer/lib/make-middleware.js:59:7)
4|customer | 2025-07-28 10:57:43 +08:00:     at indicateDone (/var/www/customer_system/backend/node_modules/multer/lib/make-middleware.js:63:68)
4|customer | 2025-07-28 10:57:43 +08:00:     at /var/www/customer_system/backend/node_modules/multer/lib/make-middleware.js:176:11
4|customer | 2025-07-28 10:57:43 +08:00:     at WriteStream.<anonymous> (/var/www/customer_system/backend/node_modules/multer/storage/disk.js:43:9)
4|customer | 2025-07-28 10:57:43 +08:00:     at WriteStream.emit (node:events:536:35)
4|customer | 2025-07-28 10:57:43 +08:00:     at finish (node:internal/streams/writable:955:10)
4|customer | 2025-07-28 10:57:43 +08:00:     at node:internal/streams/writable:936:13
4|customer | 2025-07-28 10:57:59 +08:00: 上传附件失败: TypeError: Cannot read properties of undefined (reading 'id')
4|customer | 2025-07-28 10:57:59 +08:00:     at exports.addAttachment (/var/www/customer_system/backend/src/controllers/order.controller.js:206:39)
4|customer | 2025-07-28 10:57:59 +08:00:     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
4|customer | 2025-07-28 10:57:59 +08:00:     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
4|customer | 2025-07-28 10:57:59 +08:00:     at done (/var/www/customer_system/backend/node_modules/multer/lib/make-middleware.js:59:7)
4|customer | 2025-07-28 10:57:59 +08:00:     at indicateDone (/var/www/customer_system/backend/node_modules/multer/lib/make-middleware.js:63:68)
4|customer | 2025-07-28 10:57:59 +08:00:     at Multipart.<anonymous> (/var/www/customer_system/backend/node_modules/multer/lib/make-middleware.js:187:7)
4|customer | 2025-07-28 10:57:59 +08:00:     at Multipart.emit (node:events:524:28)
4|customer | 2025-07-28 10:57:59 +08:00:     at emitCloseNT (node:internal/streams/destroy:147:10)
4|customer | 2025-07-28 10:57:59 +08:00:     at process.processTicksAndRejections (node:internal/process/task_queues:81:21)