// 引入Sequelize库和我们的数据库连接实例
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 定义 'User' 模型
 * 这个模型对应我们数据库中的 'user' 表。
 */
const User = sequelize.define('User', {
  // 定义 id 字段
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '自增主键ID'
  },
  // 定义 user_id 字段
  user_id: {
    type: DataTypes.STRING(20), // 类型：字符串，最大长度20
    unique: true,               // 业务ID，确保唯一
    allowNull: false,           // 不允许为空
    comment: '用户ID（业务ID如U202501001）'
  },
  // 定义 name 字段
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '用户姓名'
  },
  // 定义 nickname 字段
  nickname: {
    type: DataTypes.STRING(50),
    allowNull: true, // 允许为空
    comment: '昵称'
  },
  // 定义 mobile 字段
  mobile: {
    type: DataTypes.STRING(15),
    allowNull: false,
    unique: true, // 值必须唯一
    comment: '手机号'
  },
  // 定义 email 字段
  email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '邮箱'
  },
  // 定义 avatar_url 字段
  avatar_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '头像URL'
  },
  // 定义 wechat_unionid 字段
  wechat_unionid: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '微信unionid'
  },
  // 定义 wechat_openid 字段
  wechat_openid: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '微信小程序openid'
  },
  // 定义 login_type 字段
  login_type: {
    type: DataTypes.ENUM('password', 'wechat', 'partner'),
    allowNull: false,
    defaultValue: 'password',
    comment: '登录方式：password-密码登录，wechat-微信登录，partner-合伙人登录'
  },
  // 定义 is_partner 字段
  is_partner: {
    type: DataTypes.BOOLEAN, // 类型：布尔值 (在MySQL中对应TINYINT(1))
    allowNull: false,
    defaultValue: false, // 默认值为 false (0)
    comment: '是否合伙人（0否1是）'
  },
  // 定义 partner_id 字段
  partner_id: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true,
    comment: '合伙人ID（如PT0001）'
  },
  // 定义 commission_ratio 字段
  commission_ratio: {
    type: DataTypes.DECIMAL(5, 4), // 类型：高精度小数
    allowNull: true,
    comment: '分润比例（如0.1）'
  },
  // 定义 remark 字段
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  // 模型配置
  tableName: 'user', // 指定表名
  // timestamps: false, // 核心修复：注释或删除此行以启用时间戳
  comment: '用户表', // 表注释
  charset: 'utf8mb4',
  collate: 'utf8mb4_0900_ai_ci'
});

// 导出 User 模型
module.exports = User; 