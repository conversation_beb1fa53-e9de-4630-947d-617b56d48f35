const { OrderAttachment, Employee } = require('../models');
const { generateOrderId } = require('../utils/id_helper');
const orderService = require('../services/order.service'); // [修改] 引入新的订单服务
const upload = require('../middleware/upload');
const path = require('path');

/**
 * [新增] 获取下一个可用的订单ID
 */
exports.getNextOrderId = async (req, res) => {
  try {
    const nextId = await generateOrderId();
    res.status(200).json({ next_id: nextId });
  } catch (error) {
    console.error('获取下一个订单ID时出错:', error);
    res.status(500).json({ message: '生成订单ID失败', error: error.message });
  }
};

// 获取所有订单
exports.getAllOrders = async (req, res) => {
    try {
        const { enterprise_id } = req.query;

        // 权限控制：根据用户类型过滤订单
        let filterOptions = {};
        if (req.user) {
            if (req.user.type === 'user') {
                // 普通用户只能看到自己的订单
                filterOptions.user_id = req.user.id;
            } else if (req.user.type === 'employee' && req.user.role !== 'admin') {
                // 非管理员员工只能看到自己创建的订单
                filterOptions.creator_id = req.user.id;
            }
            // 管理员可以看到所有订单，不添加额外过滤条件
        }

        // 如果指定了企业ID，添加企业过滤
        if (enterprise_id) {
            filterOptions.enterprise_id = enterprise_id;
        }

        const orders = await orderService.getAllOrders(filterOptions);
        res.status(200).json(orders);
    } catch (error) {
        res.status(500).json({ message: "获取订单列表失败", error: error.message });
    }
};

// 根据ID获取单个订单
exports.getOrderById = async (req, res) => {
    try {
        const order = await orderService.getOrderById(req.params.id);
        res.status(200).json(order);
    } catch (error) {
        // 如果服务层抛出“未找到”的错误，返回404
        if (error.message === '订单未找到') {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: "获取订单详情失败", error: error.message });
    }
};

// 创建新订单及其订单项
exports.createOrder = async (req, res) => {
    try {
        const { items, ...orderData } = req.body;
        const creatorId = req.user?.id; // 从认证信息中获取创建人ID

        const newOrder = await orderService.createOrder(orderData, items, creatorId);

        res.status(201).json(newOrder);
    } catch (error) {
        console.error('创建订单失败 (Controller):', error);
        // 根据服务层可能抛出的错误类型返回不同的状态码
        if (error.message.includes('已存在')) {
            return res.status(409).json({ message: error.message });
        }
        if (error.message.includes('必须关联')) {
            return res.status(400).json({ message: error.message });
        }
        res.status(500).json({ message: '创建订单失败', error: error.message });
    }
};

// 更新订单
exports.updateOrder = async (req, res) => {
    try {
        const { id } = req.params;
        const { items, ...orderData } = req.body;

        const updatedOrder = await orderService.updateOrder(id, orderData, items);

        res.status(200).json(updatedOrder);

    } catch (error) {
        console.error('更新订单失败 (Controller):', error);
         if (error.message.includes('已被其他订单占用')) {
            return res.status(409).json({ message: error.message });
        }
        if (error.message === '订单未找到') {
            return res.status(404).json({ message: error.message });
        }
        if (error.name === 'SequelizeValidationError') {
            return res.status(400).json({ message: '数据验证失败', errors: error.errors.map(e => e.message) });
        }
        res.status(500).json({ message: "更新订单失败", error: error.message });
    }
};

// 删除订单
exports.deleteOrder = async (req, res) => {
    try {
        const result = await orderService.deleteOrder(req.params.id);
        res.status(200).json(result);
    } catch (error) {
         if (error.message === '订单未找到') {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: "删除订单失败", error: error.message });
    }
};

// -- 附件管理 --

/**
 * 为指定订单添加附件
 */
exports.addAttachment = async (req, res) => {
    try {
      const { orderId } = req.params;
      const uploaderId = req.user?.id; // 从认证信息获取上传者ID

      if (!req.file) {
        return res.status(400).send({ message: '必须上传一个文件。' });
      }

      const newAttachment = await OrderAttachment.create({
        order_id: orderId,
        filename: req.attachment_filename, // 使用中间件解码后的文件名
        file_size: req.file.size,
        file_path: req.attachment_path, // 使用中间件生成的、包含uploads/前缀的路径
        uploader_id: uploaderId,
        file_type: req.body.file_type || 'OTHER' // 从请求体获取或使用默认值
      });

      res.status(201).send(newAttachment);
    } catch (error) {
      console.error('上传订单附件失败:', error);
      res.status(500).send({ message: '上传订单附件失败', error: error.message });
    }
};

/**
 * 获取指定订单的所有附件
 */
exports.getAttachments = async (req, res) => {
  try {
    const { orderId } = req.params;
    const attachments = await OrderAttachment.findAll({
      where: { order_id: orderId },
      include: [
        { model: Employee, as: 'uploader', attributes: ['name'] } // 关联上传者信息
      ],
      order: [['uploaded_at', 'DESC']],
    });
    res.status(200).send(attachments);
  } catch (error) {
    console.error('获取订单附件失败:', error);
    res.status(500).send({ message: '获取订单附件失败', error: error.message });
  }
};

/**
 * 下载附件
 */
exports.downloadAttachment = async (req, res) => {
    try {
        const { attachmentId } = req.params;
        const attachment = await OrderAttachment.findByPk(attachmentId);
        if (!attachment) {
            return res.status(404).send({ message: '附件未找到' });
        }
        const filePath = path.join(__dirname, '..', '..', attachment.file_path);
        res.download(filePath, attachment.filename);
    } catch (error) {
        console.error('下载附件失败:', error);
        res.status(500).send({ message: '下载附件失败', error: error.message });
    }
};


/**
 * 删除指定附件
 */
exports.deleteAttachment = async (req, res) => {
  try {
    const { attachmentId } = req.params;
    const deleted = await OrderAttachment.destroy({
      where: { id: attachmentId }
    });

    if (deleted) {
      res.status(204).send(); // 204 No Content 表示成功删除
    } else {
      res.status(404).send({ message: `未找到ID为 ${attachmentId} 的附件` });
    }
  } catch (error) {
    console.error('删除订单附件失败:', error);
    res.status(500).send({ message: '删除订单附件失败', error: error.message });
  }
}; 