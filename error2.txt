root@iZ7xvczjzdx1cgmypn8ufmZ:~# cd /var/www/customer_system
root@iZ7xvczjzdx1cgmypn8ufmZ:/var/www/customer_system# # 1. 上传并执行诊断脚本
chmod +x nginx_debug.sh
./nginx_debug.sh
=== Nginx配置诊断脚本 ===

1. 检查nginx配置语法：
nginx: the configuration file /etc/nginx/nginx.conf syntax is ok
nginx: configuration file /etc/nginx/nginx.conf test is successful

2. 检查sites-enabled中的链接：
total 8
drwxr-xr-x 2 <USER> <GROUP> 4096 Jul 28 08:56 .
drwxr-xr-x 8 <USER> <GROUP> 4096 Jul 22 22:19 ..
lrwxrwxrwx 1 root root   42 Jul 22 20:40 admin.bogoo.net -> /etc/nginx/sites-available/admin.bogoo.net
lrwxrwxrwx 1 root root   35 Apr 15 17:03 beconnet -> /etc/nginx/sites-available/beconnet
lrwxrwxrwx 1 root root   34 Apr 15 15:05 default -> /etc/nginx/sites-available/default
lrwxrwxrwx 1 root root   50 Jun 17 09:41 miniprogram_images.conf -> /etc/nginx/sites-available/miniprogram_images.conf
lrwxrwxrwx 1 root root   44 Jul 28 08:56 service.bogoo.net -> /etc/nginx/sites-available/service.bogoo.net

3. 检查service.bogoo.net配置是否存在：
✓ service.bogoo.net 配置已启用

4. 检查当前生效的nginx配置：
nginx: the configuration file /etc/nginx/nginx.conf syntax is ok
nginx: configuration file /etc/nginx/nginx.conf test is successful
    server_name service.bogoo.net;

    # 增加客户端最大请求体大小，支持文件上传
    client_max_body_size 100M;

    # 小程序API接口 - 后端运行在3002端口
    location /api/ {
        # 简化配置，直接代理到后端
        proxy_pass http://127.0.0.1:3002;

        # 基本头部设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 关闭缓冲，支持POST请求
        proxy_buffering off;
        proxy_request_buffering off;

        # CORS由后端处理，nginx不再添加CORS头部
    }

    # 静态文件（如上传的图片）
    location /uploads/ {
        # 简化配置，直接代理到后端
        proxy_pass http://127.0.0.1:3002;

        # 基本头部设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
--
    server_name service.bogoo.net;
    return 404; # managed by Certbot
}

5. 检查后端服务状态：
后端服务已成功启动！
6. 测试nginx代理：
测试GET请求：
403
测试POST请求：
403
7. 检查nginx进程：
root      598214  0.0  0.0  22736  2348 ?        Ss   08:57   0:00 nginx: master process /usr/sbin/nginx -g daemon on; master_process on;
www-data  598215  0.0  0.2  24088  8492 ?        S    08:57   0:00 nginx: worker process
www-data  598216  0.0  0.2  24088  8620 ?        S    08:57   0:00 nginx: worker process
root      599980  0.0  0.1   7340  3712 pts/3    S+   09:01   0:00 /bin/bash ./nginx_debug.sh
root      599995  0.0  0.0   6544  2304 pts/3    S+   09:01   0:00 grep nginx

8. 重启nginx服务：
执行: sudo systemctl restart nginx
nginx已重启

=== 诊断完成 ===
root@iZ7xvczjzdx1cgmypn8ufmZ:/var/www/customer_system# 