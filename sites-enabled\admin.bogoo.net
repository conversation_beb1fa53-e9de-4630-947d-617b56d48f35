server {
    server_name admin.bogoo.net;
    
    # 前端静态文件 - 指向构建后的dist目录
    root /var/www/customer_system/frontend/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/admin.bogoo.net/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/admin.bogoo.net/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = admin.bogoo.net) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    server_name admin.bogoo.net;
    return 404; # managed by Certbot
}