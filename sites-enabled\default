##
# 默认服务器配置 - 处理所有未定义域名的请求
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    
    # 安全设置：禁止服务器信息泄露
    server_tokens off;
    
    # 匹配所有未配置的域名
    server_name _;
    
    # 根目录（可选保留，但实际不会使用）
    root /var/www/html;
    
    # 关键安全设置：静默关闭连接
    location / {
        return 444;
    }
    
    # 禁用所有类型的执行文件
    location ~ \.(php|asp|aspx|jsp|pl|py)$ {
        deny all;
        return 403;
    }
    
    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        return 403;
    }
    
    # 禁用自动索引
    autoindex off;
    
    # 错误日志
    access_log /var/log/nginx/default.access.log;
    error_log /var/log/nginx/default.error.log;
}