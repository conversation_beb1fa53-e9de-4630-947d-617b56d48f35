/**
 * 格式化日期时间字符串
 * @param {string | Date} isoString - 符合 ISO 8601 格式的日期字符串或 Date 对象
 * @returns {string} 格式化后的字符串，如 "2023/8/8 08:30:05"
 */
export const formatDateTime = (isoString) => {
  if (!isoString) return '';
  const d = new Date(isoString);

  // 检查日期是否有效
  if (isNaN(d.getTime())) {
    return '无效日期';
  }

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 格式化日期字符串
 * @param {string | Date} isoString - 符合 ISO 8601 格式的日期字符串或 Date 对象
 * @returns {string} 格式化后的字符串，如 "2023/08/08"
 */
export const formatDate = (isoString) => {
  if (!isoString) return '';
  const d = new Date(isoString);

  if (isNaN(d.getTime())) {
    return '无效日期';
  }

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  
  return `${year}/${month}/${day}`;
}; 