#!/bin/bash

echo "=== 修复订单相关问题 ==="
echo "1. 修复附件上传认证问题: req.employee.id -> req.user?.id || req.employee?.id"
echo "2. 添加产品订单创建调试信息"
echo

echo "重启后端服务..."
pm2 restart customer-backend

echo
echo "等待服务启动..."
sleep 3

echo "检查服务状态..."
pm2 list | grep customer-backend

echo
echo "测试服务响应..."
curl -s http://localhost:3002/ && echo " - 后端服务正常"

echo
echo "=== 修复完成 ==="
echo "现在可以测试以下功能："
echo "1. 产品订单创建（应该能看到详细的调试信息）"
echo "2. 订单附件上传（应该不再报认证错误）"
echo
echo "如果产品订单仍然需要保存两次，请检查前端是否正确传递了product_id"
