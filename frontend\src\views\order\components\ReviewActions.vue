<template>
  <div class="review-actions">
    <!-- 审核状态显示 -->
    <div class="audit-status">
      <el-tag 
        :type="getAuditStatusType(auditStatus)" 
        size="large"
        class="status-tag"
      >
        {{ auditStatus }}
      </el-tag>
      <span v-if="auditStatus === '已拒绝' && rejectReason" class="reject-reason">
        拒绝原因：{{ rejectReason }}
      </span>
    </div>

    <!-- 审核操作按钮 -->
    <div v-if="showActions" class="action-buttons">
      <!-- 审核通过 -->
      <el-button 
        type="success" 
        size="large"
        :loading="approving"
        @click="handleApprove"
        :disabled="!canApprove"
      >
        <el-icon><Check /></el-icon>
        审核通过
      </el-button>

      <!-- 拒绝审核 -->
      <el-button 
        type="danger" 
        size="large"
        :loading="rejecting"
        @click="handleReject"
      >
        <el-icon><Close /></el-icon>
        拒绝审核
      </el-button>

      <!-- 补全信息 -->
      <el-button 
        type="warning" 
        size="large"
        @click="handleComplete"
      >
        <el-icon><Edit /></el-icon>
        补全信息
      </el-button>

      <!-- 删除订单 -->
      <el-button 
        type="danger" 
        plain
        size="large"
        :loading="deleting"
        @click="handleDelete"
      >
        <el-icon><Delete /></el-icon>
        删除订单
      </el-button>
    </div>

    <!-- 审核历史 -->
    <div v-if="auditHistory && auditHistory.length > 0" class="audit-history">
      <h5>审核历史</h5>
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in auditHistory"
          :key="index"
          :timestamp="formatDateTime(item.created_at)"
          :type="getTimelineType(item.action)"
        >
          <div class="history-item">
            <div class="action-info">
              <span class="action">{{ item.action }}</span>
              <span class="operator">{{ item.operator }}</span>
            </div>
            <div v-if="item.remark" class="remark">
              {{ item.remark }}
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 审核说明 -->
    <div class="audit-instructions">
      <el-alert
        title="审核说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <ul>
            <li><strong>审核通过：</strong>订单信息完整且正确，支付状态为"已支付"的订单可以审核通过</li>
            <li><strong>拒绝审核：</strong>订单信息有误或不完整时，可以拒绝审核并说明原因</li>
            <li><strong>补全信息：</strong>订单信息不完整时，可以进入编辑模式补全信息</li>
            <li><strong>删除订单：</strong>错误创建的订单可以删除，此操作不可恢复</li>
          </ul>
        </template>
      </el-alert>
    </div>

    <!-- 拒绝原因对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="拒绝审核"
      width="500px"
      :before-close="closeRejectDialog"
    >
      <el-form :model="rejectForm" :rules="rejectRules" ref="rejectFormRef">
        <el-form-item label="拒绝原因" prop="reason">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeRejectDialog">取消</el-button>
          <el-button 
            type="danger" 
            :loading="rejecting"
            @click="confirmReject"
          >
            确认拒绝
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Check, 
  Close, 
  Edit, 
  Delete 
} from '@element-plus/icons-vue';
import { approveOrder, rejectOrder, deleteOrder } from '@/api/order';
import { formatDateTime } from '@/utils/format';

// Props
const props = defineProps({
  orderId: {
    type: [String, Number],
    required: true
  },
  auditStatus: {
    type: String,
    default: '待审核'
  },
  rejectReason: {
    type: String,
    default: ''
  },
  orderData: {
    type: Object,
    default: () => ({})
  },
  auditHistory: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['audit-success', 'delete-success', 'edit-mode']);

// 路由
const router = useRouter();

// 响应式数据
const approving = ref(false);
const rejecting = ref(false);
const deleting = ref(false);
const rejectDialogVisible = ref(false);
const rejectFormRef = ref(null);

const rejectForm = ref({
  reason: ''
});

const rejectRules = {
  reason: [
    { required: true, message: '请输入拒绝原因', trigger: 'blur' },
    { min: 5, message: '拒绝原因至少5个字符', trigger: 'blur' }
  ]
};

// 计算属性：是否显示操作按钮
const showActions = computed(() => {
  return props.auditStatus === '待审核';
});

// 计算属性：是否可以审核通过
const canApprove = computed(() => {
  // 产品订单需要支付状态为"已支付"才能审核通过
  if (props.orderData.order_category === '产品订单') {
    return props.orderData.payment_status === '已支付';
  }
  // 服务订单可以直接审核通过
  return true;
});

// 获取审核状态标签类型
const getAuditStatusType = (status) => {
  switch (status) {
    case '待审核':
      return 'warning';
    case '已审核':
      return 'success';
    case '已拒绝':
      return 'danger';
    default:
      return 'info';
  }
};

// 获取时间线类型
const getTimelineType = (action) => {
  switch (action) {
    case '审核通过':
      return 'success';
    case '拒绝审核':
      return 'danger';
    case '补全信息':
      return 'warning';
    default:
      return 'primary';
  }
};

// 审核通过
const handleApprove = async () => {
  if (!canApprove.value) {
    ElMessage.warning('产品订单需要支付状态为"已支付"才能审核通过');
    return;
  }

  try {
    await ElMessageBox.confirm(
      '确定要审核通过此订单吗？审核通过后订单将流转到对应的订单列表。',
      '确认审核通过',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    approving.value = true;
    await approveOrder(props.orderId);
    
    ElMessage.success('审核通过成功');
    emit('audit-success', 'approved');
    
    // 跳转到对应的订单列表
    const routeName = props.orderData.order_category === '产品订单' 
      ? 'ProductOrderList' 
      : 'ServiceOrderList';
    router.push({ name: routeName });
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核通过失败:', error);
      ElMessage.error('审核通过失败');
    }
  } finally {
    approving.value = false;
  }
};

// 拒绝审核
const handleReject = () => {
  rejectForm.value.reason = '';
  rejectDialogVisible.value = true;
};

// 确认拒绝
const confirmReject = async () => {
  try {
    await rejectFormRef.value.validate();
    
    rejecting.value = true;
    await rejectOrder(props.orderId, rejectForm.value.reason);
    
    ElMessage.success('拒绝审核成功');
    emit('audit-success', 'rejected');
    closeRejectDialog();
    
  } catch (error) {
    if (error.message) {
      console.error('拒绝审核失败:', error);
      ElMessage.error('拒绝审核失败');
    }
  } finally {
    rejecting.value = false;
  }
};

// 关闭拒绝对话框
const closeRejectDialog = () => {
  rejectDialogVisible.value = false;
  rejectForm.value.reason = '';
};

// 补全信息
const handleComplete = () => {
  emit('edit-mode');
  ElMessage.info('已切换到编辑模式，请补全订单信息');
};

// 删除订单
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除此订单吗？此操作不可恢复，请谨慎操作。',
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    deleting.value = true;
    await deleteOrder(props.orderId);
    
    ElMessage.success('删除成功');
    emit('delete-success');
    
    // 跳转到审核列表
    router.push({ name: 'OrderReviewList' });
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除订单失败:', error);
      ElMessage.error('删除订单失败');
    }
  } finally {
    deleting.value = false;
  }
};
</script>

<style scoped>
.review-actions {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.audit-status {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.status-tag {
  font-size: 14px;
  padding: 8px 16px;
}

.reject-reason {
  color: #f56c6c;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  min-width: 120px;
}

.audit-history {
  margin: 20px 0;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.audit-history h5 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
}

.history-item {
  padding: 8px 0;
}

.action-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.action {
  font-weight: 500;
  color: #303133;
}

.operator {
  color: #909399;
  font-size: 12px;
}

.remark {
  color: #606266;
  font-size: 12px;
  background: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 8px;
}

.audit-instructions {
  margin-top: 20px;
}

.audit-instructions ul {
  margin: 0;
  padding-left: 20px;
}

.audit-instructions li {
  margin-bottom: 8px;
  color: #606266;
}

.audit-instructions li:last-child {
  margin-bottom: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
  
  .audit-status {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
