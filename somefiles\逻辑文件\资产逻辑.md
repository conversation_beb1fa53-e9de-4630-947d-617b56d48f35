# 一级菜单：资产管理
# 二级菜单：新增资产、资产查看、变更资产、变更列表

## 1. 新增资产
### 1.1. 表头
- **资产ID**：
- **企业ID**：
- **用户ID**：
- **status**：
### 1.2. 表体
#### 1.2.1. 产品详情
- **产品ID-版本号**：
- **使用人数**：
- **账套数**：
- **购买时长**：单位为月
- **勾选产品功能**：

- **购买日期**：
- **到期日-产品到期日**：
- **到期日-sps到期日**：
- **到期日-服务到期日**：

- **价格信息-产品标准价**：
- **价格信息-sps年费**：
- **价格信息-售后服务费用**：
- **价格信息-实施费用**：

- **关联订单**：按钮
- 若新增订单时，订单里还未绑定资产，那么此时新增资产时，在此处可过滤出关联该**企业**下的未绑定资产的全部**订单列表**信息，手工匹配。

#### 1.2.2. 激活信息
- **激活码**：
- **激活手机号**：
- **激活密码**：
### 1.3. 表尾
- **备注**：
- **制单人**：当前登录的员工
- **制单时间**：创建表单一个时间，保存时确定时间？？？


## 2. 资产列表
- 展示所有资产列表，点击资产列表中的**资产编码**，可进入资产详情查看页面

## 3. 查看资产（修改资产时展示相同的内容字段）
### 3.1. 表头
- **资产ID**：
- **企业ID**：
- **用户ID**：
- **status**：
### 3.2. 表体
#### 3.2.1. 产品详情
- **产品ID-版本号**：
- **使用人数**：
- **账套数**：
- **购买时长**：单位为月
- **勾选产品功能**：

- **购买日期**：
- **到期日-产品到期日**：
- **到期日-sps到期日**：
- **到期日-服务到期日**：

- **价格信息-产品标准价**：
- **价格信息-sps年费**：
- **价格信息-售后服务费用**：
- **价格信息-实施费用**：
#### 3.2.2. 激活信息
- **激活码**：
- **激活手机号**：
- **激活密码**：
#### 3.2.3. 变更记录（关联该资产的全部变更列表：资产变更ID、变更时间、变更备注）
#### 3.2.4. 关联订单（关联该资产的全部订单列表）
### 3.3. 表尾
- **备注**：
- **制单人**：当前登录的员工
- **制单时间**：创建表单一个时间，保存时确定时间？？？

## 4. 变更资产（变更功能、人数、账套、版本？？？）
- 前端展示：变更资产单，为左右两列，左侧为资产原始所有信息（不可修改），右侧默认带出原始所有信息，但支持变更。
- 新增资产变更单，表体字段均可变更！
### 4.1. 表头
- **资产变更ID**：
- **变更日期**：
- **资产ID**：
- **企业ID**：
- **用户ID**：
- **status**：
### 4.2. 表体
#### 4.2.1. 产品详情
- **产品ID-版本号**：
- **使用人数**：
- **账套数**：
- **购买时长**：单位为月
- **勾选产品功能**：

- **购买日期**：
- **到期日-产品到期日**：
- **到期日-sps到期日**：
- **到期日-服务到期日**：

- **价格信息-产品标准价**：
- **价格信息-sps年费**：
- **价格信息-售后服务费用**：
- **价格信息-实施费用**：
#### 4.2.2. 激活信息
- **激活码**：
- **激活手机号**：
- **激活密码**：
#### 4.2.3. 关联变更订单
- 在此处可过滤出关联该**资产**的全部**订单列表**信息，手工关联变更相关的订单。
### 4.3. 表尾
- **备注**：自动生成变更字段信息、变更前后信息，可手工修改。
- **制单人**：当前登录的员工
- **制单时间**：创建表单一个时间，保存时确定时间？？？

## 5. 资产变更列表
- 展示所有资产变更单的列表，点击资产列表中的**资产变更ID**，可进入资产变更单详情查看页面
- 展示字段：资产变更ID、变更时间、变更备注


# 注意事项：
- 查看资产时，需要有个页签，展示关联到这个资产对应的所有订单；但是，新增资产时，只需要一个按钮，关联到触发这次新增的“订单”，这个主要是因为：有时候还没创建资产，已经新增订单时，订单里还未绑定资产，这里关联后，订单里的“资产”字段也会字段关联该资产。
- 新增资产变更单时，也需要有个按钮，关联到触发这次变更的“订单”。且查看这个“资产变更单”时，可以查看到关联的订单，支持联查该功能。变更资产数据库表中，变更前、变更后两个字段是json格式储存的信息。
- 资产的新增、查看、修改页面的UI结构应该保持一致性！变更资产时，前端并排展示变更前和变更后的信息的UI结构也应该尽可能的与“资产的新增、查看、修改页面”UI结构一致。
- 我需要查看、修改资产的表单里以标签页的形式展示“表体信息”，标签页名分别为：产品详情、激活信息、关联订单、变更记录 这种。增加资产的表单里，标签页名分别为：产品详情、激活信息、关联订单。
- 核心功能完整：资产的 列表、新增、查看、修改、删除、变更 六大核心功能全部实现。
- 强大的变更流程：实现左右分栏对比的变更视图，并支持关联变更订单。
- 完善的追溯机制：详情页中可以清晰地查看资产的历史变更记录，并支持一键回滚。
- 灵活的订单关联：实现了在新增资产时关联初始订单、在变更资产时关联变更订单的完整逻辑。
- 关联订单的相关功能，都需要支持关联订单列表里，支持联查具体的表单信息！