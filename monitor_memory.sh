#!/bin/bash

echo "=== 后端内存监控脚本 ==="
echo "时间: $(date)"
echo

echo "1. PM2进程状态:"
pm2 list

echo
echo "2. 详细内存信息:"
pm2 show customer-backend | grep -E "(memory|heap|restarts|uptime)"

echo
echo "3. 系统内存使用:"
free -h

echo
echo "4. Node.js进程内存:"
ps aux | grep "node.*index.js" | grep -v grep

echo
echo "5. 最近的错误日志:"
echo "--- 错误日志 (最近10行) ---"
tail -10 /root/.pm2/logs/customer-backend-error.log 2>/dev/null || echo "无错误日志"

echo
echo "6. 最近的输出日志:"
echo "--- 输出日志 (最近10行) ---"
tail -10 /root/.pm2/logs/customer-backend-out.log 2>/dev/null || echo "无输出日志"

echo
echo "7. 数据库连接测试:"
echo "测试数据库连接..."
curl -s -m 5 http://localhost:3002/ && echo " - 后端服务响应正常" || echo " - 后端服务无响应"

echo
echo "=== 监控完成 ==="
echo "如果重启次数持续增加，说明仍有内存泄漏问题"
echo "正常情况下，内存使用应该稳定在512M以下"
