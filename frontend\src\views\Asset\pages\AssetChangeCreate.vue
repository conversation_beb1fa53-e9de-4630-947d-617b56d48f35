<template>
  <div class="asset-change-create-page" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="handleBack" size="default" class="back-button">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <h2 class="page-title">{{ pageTitle }}</h2>
        </div>
        
        <div class="header-actions">
          <el-button type="primary" @click="handleSave" :loading="saving">
            <el-icon><Check /></el-icon>
            保存变更
          </el-button>
          <el-button @click="handleCancel">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="change-content" v-if="originalAssetData && changedAssetData && changeFormData">
      <!-- 资产变更表单表头 -->
      <AssetChangeFormHeader
        :formData="changeFormData"
        :readonly="false"
        :originalData="originalAssetData"
        :changedData="changedAssetData"
        @data-change="handleChangeFormDataChange"
        ref="changeHeaderRef"
      />

      <!-- 资产变更对比组件 -->
      <AssetChangeCompare
        :originalData="originalAssetData"
        :changedData="changedAssetData"
        :editMode="true"
        :enterpriseOptions="enterpriseOptions"
        :userOptions="userOptions"
        :productOptions="productOptions"
        @data-change="handleAssetDataChange"
        ref="compareRef"
      />

      <!-- 关联变更订单标签页 -->
      <div class="related-orders-section">
        <el-card shadow="never" class="orders-card">
          <template #header>
            <span class="card-header">
              <el-icon><Document /></el-icon>
              关联变更订单
            </span>
          </template>
          
          <AssetRelatedOrders
            mode="create"
            :readonly="false"
            :enterpriseId="originalAssetData.enterprise_id"
            :selectedOrderIds="selectedChangeOrderIds"
            @orders-change="handleChangeOrdersChange"
          />
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, Check, Close, Document
} from '@element-plus/icons-vue'

// 组件导入
import AssetChangeFormHeader from '../components/AssetChangeFormHeader.vue'
import AssetChangeCompare from '../components/AssetChangeCompare.vue'
import AssetRelatedOrders from '../components/AssetRelatedOrders.vue'

// Composables
import { useAssetData } from '../composables/useAssetData.js'
import { useAuth } from '@/store/auth.js'

// API
import { getEnterprises } from '@/api/enterprise.js'
import { getUsers } from '@/api/user.js'
import { getProducts } from '@/api/product.js'
import { createAssetChange, getNextAssetChangeId } from '@/api/asset.js'

// 路由
const route = useRoute()
const router = useRouter()

// 数据管理
const { loading, fetchAsset } = useAssetData()

// 认证状态
const { state: authState } = useAuth()

// 状态
const saving = ref(false)
const originalAssetData = ref(null)
const changedAssetData = ref(null)
const changeFormData = ref(null)
const selectedChangeOrderIds = ref([])

// 表单引用
const changeHeaderRef = ref(null)
const compareRef = ref(null)

// 选项数据
const enterpriseOptions = ref([])
const userOptions = ref([])
const productOptions = ref([])

// 计算属性
const assetId = computed(() => route.params.id)
const pageTitle = computed(() => {
  if (originalAssetData.value) {
    return `变更资产 - ${originalAssetData.value.asset_id}`
  }
  return '变更资产'
})

// 加载选项数据
const loadOptions = async () => {
  try {
    const [enterprises, users, products] = await Promise.all([
      getEnterprises(),
      getUsers(),
      getProducts()
    ])

    enterpriseOptions.value = enterprises || []
    userOptions.value = users || []
    productOptions.value = products || []
  } catch (error) {
    console.error('加载选项数据失败:', error)
    ElMessage.error('加载选项数据失败')
  }
}

// 确保数字字段的类型正确
const normalizeNumberFields = (data) => {
  if (!data) return data

  const numberFields = [
    'user_count', 'account_count', 'duration_months',
    'product_standard_price', 'sps_annual_fee', 'after_sales_service_fee', 'implementation_fee'
  ]

  const normalized = { ...data }
  numberFields.forEach(field => {
    if (normalized[field] !== undefined && normalized[field] !== null && normalized[field] !== '') {
      const numValue = Number(normalized[field])
      if (!isNaN(numValue)) {
        normalized[field] = numValue
      }
    }
  })

  return normalized
}

// 加载原始资产数据
const loadOriginalAssetData = async () => {
  if (!assetId.value) {
    ElMessage.error('缺少资产ID参数')
    return
  }

  try {
    const data = await fetchAsset(assetId.value)
    // 确保数字字段类型正确
    originalAssetData.value = normalizeNumberFields(data)

    // 初始化变更后的数据（深拷贝原始数据）
    changedAssetData.value = normalizeNumberFields(JSON.parse(JSON.stringify(data)))
  } catch (error) {
    console.error('加载资产数据失败:', error)
    ElMessage.error('加载资产数据失败')
  }
}

// 初始化变更表单数据
const initializeChangeFormData = async () => {
  try {
    const response = await getNextAssetChangeId()
    const changeId = response.next_id || response // 兼容不同的响应格式
    changeFormData.value = {
      asset_change_id: changeId,
      change_date: new Date(),
      creator: authState.user,
      createdAt: new Date(),
      remark: ''
    }
  } catch (error) {
    console.error('初始化变更表单数据失败:', error)
    ElMessage.error('初始化变更表单数据失败')
  }
}

// 事件处理
const handleBack = () => {
  router.back()
}

const handleCancel = () => {
  router.push({ name: 'asset-detail', params: { id: assetId.value } })
}

const handleChangeFormDataChange = () => {
  // 变更表单数据变化处理
}

const handleAssetDataChange = (newData) => {
  // 资产数据变化处理，确保数字字段类型正确
  changedAssetData.value = normalizeNumberFields({ ...changedAssetData.value, ...newData })
}

const handleChangeOrdersChange = (orderIds) => {
  selectedChangeOrderIds.value = orderIds
}

// 表单验证
const validateForm = async () => {
  try {
    const headerValid = await changeHeaderRef.value?.validate()
    const compareValid = await compareRef.value?.validate()
    
    return headerValid && compareValid
  } catch (error) {
    return false
  }
}

// 保存变更
const handleSave = async () => {
  // 验证表单
  const isValid = await validateForm()
  if (!isValid) {
    ElMessage.error('请检查表单数据')
    return
  }

  // 检查是否有实际变更
  const hasChanges = JSON.stringify(originalAssetData.value) !== JSON.stringify(changedAssetData.value)
  if (!hasChanges) {
    ElMessage.warning('没有检测到任何变更')
    return
  }

  saving.value = true
  try {
    const changeData = {
      ...changeFormData.value,
      asset_id: assetId.value,
      snapshot_before: originalAssetData.value,
      snapshot_after: changedAssetData.value,
      change_order_ids: selectedChangeOrderIds.value
    }

    await createAssetChange(changeData)
    ElMessage.success('资产变更保存成功')
    
    // 跳转到资产详情页
    router.push({ name: 'asset-detail', params: { id: assetId.value } })
  } catch (error) {
    console.error('保存变更失败:', error)
    ElMessage.error(`保存变更失败: ${error.message || '未知错误'}`)
  } finally {
    saving.value = false
  }
}

// 生命周期
onMounted(async () => {
  await loadOptions()
  await loadOriginalAssetData()
  await initializeChangeFormData()
})
</script>

<style scoped>
.asset-change-create-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(240, 147, 251, 0.3);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .el-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.header-actions .el-button--primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 变更内容 */
.change-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 关联订单区域 */
.related-orders-section {
  margin-top: 20px;
}

.orders-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .asset-change-create-page {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .page-title {
    font-size: 20px;
  }
}
</style>
