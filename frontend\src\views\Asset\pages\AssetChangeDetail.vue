<template>
  <div class="asset-change-detail-page" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="handleBack" size="default" class="back-button">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <h2 class="page-title">{{ pageTitle }}</h2>
        </div>
        
        <div class="header-actions">
          <el-button type="warning" @click="handleRollback" v-if="changeData">
            <el-icon><RefreshLeft /></el-icon>
            回滚到变更前状态
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="detail-content" v-if="changeData && beforeData && afterData">
      <!-- 资产变更表单表头（只读） -->
      <AssetChangeFormHeader
        :formData="changeData"
        :readonly="true"
        ref="changeHeaderRef"
      />

      <!-- 资产变更对比组件（只读） -->
      <AssetChangeCompare
        :originalData="beforeData"
        :changedData="afterData"
        :editMode="false"
        :enterpriseOptions="enterpriseOptions"
        :userOptions="userOptions"
        :productOptions="productOptions"
        ref="compareRef"
      />

      <!-- 关联变更订单信息 -->
      <div class="related-orders-section">
        <el-card shadow="never" class="orders-card">
          <template #header>
            <span class="card-header">
              <el-icon><Document /></el-icon>
              关联变更订单
              <span class="order-count">({{ relatedOrders.length }})</span>
            </span>
          </template>

          <div v-if="relatedOrders.length > 0">
            <el-table :data="relatedOrders" border style="width: 100%">
              <el-table-column prop="order_id" label="订单ID" width="180">
                <template #default="{ row }">
                  <el-button
                    link
                    type="primary"
                    @click="viewOrderDetail(row.id)"
                  >
                    {{ row.order_id }}
                  </el-button>
                </template>
              </el-table-column>

              <el-table-column prop="order_type" label="订单类型" width="120" />

              <el-table-column prop="total_amount" label="订单金额" width="120" align="right">
                <template #default="{ row }">
                  <span class="amount-text">¥{{ formatAmount(row.total_amount) }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="payment_status" label="支付状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getPaymentStatusType(row.payment_status)">
                    {{ row.payment_status }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="created_at" label="创建时间" width="180">
                <template #default="{ row }">
                  {{ formatDateTime(row.created_at) }}
                </template>
              </el-table-column>

              <el-table-column prop="remark" label="备注" show-overflow-tooltip />
            </el-table>
          </div>

          <div v-else class="no-orders">
            <el-empty
              description="暂无关联订单"
              :image-size="80"
            />
          </div>
        </el-card>
      </div>


    </div>

    <!-- 回滚确认对话框 -->
    <RollbackDialog
      v-model="showRollbackDialog"
      :rollback-record="rollbackRecord"
      :loading="rollbackLoading"
      @confirm="confirmRollback"
      @cancel="cancelRollback"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft, RefreshLeft, Document
} from '@element-plus/icons-vue'

// 组件导入
import AssetChangeFormHeader from '../components/AssetChangeFormHeader.vue'
import AssetChangeCompare from '../components/AssetChangeCompare.vue'
import RollbackDialog from '../components/RollbackDialog.vue'

// 工具函数
import { formatDateTime } from '@/utils/format.js'

// API
import { getEnterprises } from '@/api/enterprise.js'
import { getUsers } from '@/api/user.js'
import { getProducts } from '@/api/product.js'
import { getAssetChangeDetail } from '@/api/asset.js'
import { useAssetData } from '../composables/useAssetData.js'

// 路由
const route = useRoute()
const router = useRouter()

// 使用 composable
const {
  rollbackLoading,
  showRollbackDialog,
  rollbackRecord,
  showRollbackConfirm,
  executeRollback,
  cancelRollback
} = useAssetData()

// 状态
const loading = ref(false)
const changeData = ref(null)
const beforeData = ref(null)
const afterData = ref(null)
const relatedOrders = ref([])

// 表单引用
const changeHeaderRef = ref(null)
const compareRef = ref(null)

// 选项数据
const enterpriseOptions = ref([])
const userOptions = ref([])
const productOptions = ref([])

// 计算属性
const changeId = computed(() => route.params.id)
const pageTitle = computed(() => {
  if (changeData.value) {
    return `变更详情 - ${changeData.value.asset_change_id}`
  }
  return '变更详情'
})



// 加载选项数据
const loadOptions = async () => {
  try {
    const [enterprises, users, products] = await Promise.all([
      getEnterprises(),
      getUsers(),
      getProducts()
    ])

    enterpriseOptions.value = enterprises || []
    userOptions.value = users || []
    productOptions.value = products || []
  } catch (error) {
    console.error('加载选项数据失败:', error)
  }
}

// 加载变更详情数据
const loadChangeDetail = async () => {
  if (!changeId.value) {
    ElMessage.error('缺少变更ID参数')
    return
  }

  loading.value = true
  try {
    const data = await getAssetChangeDetail(changeId.value)
    
    changeData.value = data
    beforeData.value = data.snapshot_before
    afterData.value = data.snapshot_after
    relatedOrders.value = data.related_orders || []
  } catch (error) {
    console.error('加载变更详情失败:', error)
    ElMessage.error('加载变更详情失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleBack = () => {
  router.back()
}

const handleRollback = () => {
  // 构造回滚记录对象
  const record = {
    id: changeId.value,
    asset_change_id: changeData.value.asset_change_id,
    created_at: changeData.value.change_date,
    created_by: changeData.value.creator?.name || '未知'
  }

  showRollbackConfirm(record)
}

const confirmRollback = async () => {
  await executeRollback(changeData.value.asset_id, async () => {
    // 跳转到资产详情页
    router.push({
      name: 'asset-detail',
      params: { id: changeData.value.asset_id }
    })
  })
}

// 查看订单详情
const viewOrderDetail = (orderId) => {
  router.push({ name: 'order-detail', params: { id: orderId } })
}

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toFixed(2)
}

// 获取订单状态类型
const getOrderStatusType = (status) => {
  const statusMap = {
    '待确认': 'warning',
    '已确认': 'success',
    '已完成': 'success',
    '已取消': 'danger',
    '已关闭': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态类型
const getPaymentStatusType = (status) => {
  const statusMap = {
    '待支付': 'warning',
    '已支付': 'success'
  }
  return statusMap[status] || 'info'
}



// 生命周期
onMounted(async () => {
  await loadOptions()
  await loadChangeDetail()
})
</script>

<style scoped>
.asset-change-detail-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(79, 172, 254, 0.3);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .el-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 详情内容 */
.detail-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 关联订单区域 */
.related-orders-section {
  margin-top: 20px;
}

.orders-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.amount-text {
  font-weight: 600;
  color: #f56c6c;
}

.related-orders-section {
  margin-top: 24px;
}

.orders-card {
  border: 1px solid #e4e7ed;
}

.order-count {
  color: #909399;
  font-weight: normal;
  margin-left: 8px;
}

.no-orders {
  padding: 40px 0;
  text-align: center;
}



/* 响应式调整 */
@media (max-width: 768px) {
  .asset-change-detail-page {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .page-title {
    font-size: 20px;
  }
}
</style>
