#!/bin/bash

echo "=== 后端服务重启脚本 ==="
echo "时间: $(date)"
echo

echo "1. 停止当前后端服务..."
pm2 stop customer-backend
sleep 2

echo "2. 删除旧的进程..."
pm2 delete customer-backend
sleep 1

echo "3. 使用新配置启动服务..."
pm2 start ecosystem.config.js
sleep 3

echo "4. 检查服务状态..."
pm2 list

echo
echo "5. 检查内存使用情况..."
pm2 show customer-backend

echo
echo "6. 查看最新日志..."
pm2 logs customer-backend --lines 20

echo
echo "7. 测试服务响应..."
echo "测试基本连接:"
curl -s -o /dev/null -w "HTTP状态码: %{http_code}, 响应时间: %{time_total}s\n" http://localhost:3002/

echo
echo "测试API接口:"
curl -s -o /dev/null -w "HTTP状态码: %{http_code}, 响应时间: %{time_total}s\n" http://localhost:3002/api/enterprises

echo
echo "=== 重启完成 ==="
echo "如果服务正常，重启次数应该重置为0"
echo "监控命令: pm2 monit"
echo "日志命令: pm2 logs customer-backend --lines 50"
