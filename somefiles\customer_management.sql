/*
 Navicat Premium Data Transfer

 Source Server         : w
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42-0ubuntu0.24.04.1)
 Source Host           : *************:3306
 Source Schema         : customer_management

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42-0ubuntu0.24.04.1)
 File Encoding         : 65001

 Date: 28/07/2025 13:57:54
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for asset
-- ----------------------------
DROP TABLE IF EXISTS `asset`;
CREATE TABLE `asset`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `asset_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资产业务ID',
  `enterprise_id` int NOT NULL COMMENT '关联enterprise.id',
  `user_id` int NULL DEFAULT NULL COMMENT '关联user.id',
  `status` enum('过期','在线') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '在线' COMMENT '资产状态',
  `product_id` int NOT NULL COMMENT '关联product.id',
  `user_count` int NOT NULL COMMENT '使用人数',
  `account_count` int NOT NULL COMMENT '账套数',
  `duration_months` int NULL DEFAULT NULL COMMENT '购买时长（月）',
  `selected_features` json NULL COMMENT '功能ID列表',
  `purchase_date` date NULL DEFAULT NULL COMMENT '购买日期',
  `product_expiry_date` date NULL DEFAULT NULL COMMENT '产品到期日',
  `sps_expiry_date` date NULL DEFAULT NULL COMMENT 'SPS到期日（原service_expiry_date）',
  `after_sales_expiry_date` date NULL DEFAULT NULL COMMENT '售后服务到期日',
  `product_standard_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '产品标准价',
  `sps_annual_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT 'SPS年费（即应用服务费）',
  `after_sales_service_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '售后服务费用',
  `implementation_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '实施费用',
  `activation_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '激活码',
  `activation_phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '激活手机号',
  `activation_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '激活密码（明文存储）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `creator_id` int NULL DEFAULT NULL COMMENT '制单人（关联employee.id）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `asset_id`(`asset_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_enterprise_id`(`enterprise_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_creator_id`(`creator_id` ASC) USING BTREE,
  CONSTRAINT `fk_asset_creator` FOREIGN KEY (`creator_id`) REFERENCES `employee` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_asset_to_enterprise` FOREIGN KEY (`enterprise_id`) REFERENCES `enterprise` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_asset_to_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_asset_to_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for asset_change_log
-- ----------------------------
DROP TABLE IF EXISTS `asset_change_log`;
CREATE TABLE `asset_change_log`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `asset_change_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资产变更业务ID（可手工修改）',
  `change_date` date NULL DEFAULT NULL COMMENT '变更日期（手填）',
  `asset_id` int NOT NULL COMMENT '关联asset.id',
  `snapshot_before` json NOT NULL COMMENT '变更前快照',
  `snapshot_after` json NOT NULL COMMENT '变更后快照',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `creator_id` int NOT NULL COMMENT '制单人ID（关联employee.id）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_asset_id`(`asset_id` ASC) USING BTREE,
  INDEX `idx_employee_id`(`creator_id` ASC) USING BTREE,
  CONSTRAINT `asset_change_log_ibfk_6` FOREIGN KEY (`creator_id`) REFERENCES `employee` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_asset_change_log_to_asset` FOREIGN KEY (`asset_id`) REFERENCES `asset` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for employee
-- ----------------------------
DROP TABLE IF EXISTS `employee`;
CREATE TABLE `employee`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `employee_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '员工工号（如SW008）',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '员工姓名',
  `mobile` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  `department` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门',
  `password` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '加密密码',
  `role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'employee' COMMENT '用户角色: admin, employee',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_employee_number`(`employee_number` ASC) USING BTREE,
  UNIQUE INDEX `idx_mobile`(`mobile` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for enterprise
-- ----------------------------
DROP TABLE IF EXISTS `enterprise`;
CREATE TABLE `enterprise`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `enterprise_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业ID（如EN20250001）',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业名称',
  `tax_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业税号',
  `bank_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '开户行',
  `bank_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '银行账户',
  `invoice_type` enum('普票','专票') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '普票' COMMENT '开票类型',
  `contact_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地址',
  `license_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '营业执照路径',
  `employee_id` int NULL DEFAULT NULL COMMENT '负责员工ID',
  `user_id` int NULL DEFAULT NULL COMMENT '关联user.id',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_enterprise_id_unique`(`enterprise_id` ASC) USING BTREE,
  INDEX `idx_employee_id`(`employee_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `enterprise_ibfk_5` FOREIGN KEY (`employee_id`) REFERENCES `employee` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_enterprise_to_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for followup
-- ----------------------------
DROP TABLE IF EXISTS `followup`;
CREATE TABLE `followup`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `enterprise_id` int NOT NULL COMMENT '关联enterprise.id',
  `followup_time` datetime(6) NOT NULL COMMENT '跟进时间',
  `situation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '跟进情况',
  `attachment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件路径（多文件用,分隔）',
  `employee_id` int NOT NULL COMMENT '跟进员工ID',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_employee_id`(`employee_id` ASC) USING BTREE,
  INDEX `idx_enterprise_id`(`enterprise_id` ASC) USING BTREE,
  CONSTRAINT `fk_followup_to_enterprise` FOREIGN KEY (`enterprise_id`) REFERENCES `enterprise` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `followup_ibfk_6` FOREIGN KEY (`employee_id`) REFERENCES `employee` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_attachment
-- ----------------------------
DROP TABLE IF EXISTS `order_attachment`;
CREATE TABLE `order_attachment`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL COMMENT '关联order_head.id',
  `filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` int NULL DEFAULT NULL COMMENT '文件大小(字节)',
  `file_type` enum('合同','发票','其他') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '其他',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `uploader_id` int NULL DEFAULT NULL COMMENT '上传人(关联employee.id)',
  `uploaded_at` datetime NOT NULL,
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id` ASC) USING BTREE,
  INDEX `uploader_id`(`uploader_id` ASC) USING BTREE,
  CONSTRAINT `order_attachment_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `order_head` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `order_attachment_ibfk_2` FOREIGN KEY (`uploader_id`) REFERENCES `employee` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_head
-- ----------------------------
DROP TABLE IF EXISTS `order_head`;
CREATE TABLE `order_head`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单业务ID（PO开头=产品订单，SO开头=服务订单）',
  `order_category` enum('产品订单','服务订单') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单大类',
  `enterprise_id` int NULL DEFAULT NULL COMMENT '关联enterprise.id',
  `asset_id` int NULL DEFAULT NULL COMMENT '关联asset.id',
  `user_id` int NULL DEFAULT NULL COMMENT '关联user.id',
  `creation_method` enum('手工创建','用户创建') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建方式',
  `order_type` enum('普通订单','续费订单','变更订单') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单类型',
  `standard_amount` decimal(10, 2) NOT NULL COMMENT '订单标准金额',
  `actual_amount` decimal(10, 2) NOT NULL COMMENT '订单实付金额',
  `tax_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '税额（仅产品订单使用）',
  `invoice_type` enum('不开票','普票','专票') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '不开票' COMMENT '发票类型（仅产品订单使用）',
  `payment_status` enum('待支付','已支付') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '待支付' COMMENT '支付状态（仅产品订单使用）',
  `payment_method` enum('在线','对公') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '支付方式（仅产品订单使用）',
  `payment_time` datetime NULL DEFAULT NULL COMMENT '支付时间（仅产品订单使用）',
  `is_partner_order` tinyint(1) NULL DEFAULT 0 COMMENT '是否合伙人订单',
  `partner_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '合伙人ID',
  `commission_ratio` decimal(5, 4) NULL DEFAULT NULL COMMENT '分润比例',
  `commission_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '分润金额',
  `commission_status` enum('已发放','未发放') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '未发放',
  `audit_status` enum('待审核','已审核','已拒绝') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '待审核' COMMENT '审核状态',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  `creator_id` int NULL DEFAULT NULL COMMENT '制单人（关联employee.id）',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `enterprise_id`(`enterprise_id` ASC) USING BTREE,
  INDEX `asset_id`(`asset_id` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `creator_id`(`creator_id` ASC) USING BTREE,
  CONSTRAINT `order_head_ibfk_1` FOREIGN KEY (`enterprise_id`) REFERENCES `enterprise` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `order_head_ibfk_2` FOREIGN KEY (`asset_id`) REFERENCES `asset` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `order_head_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `order_head_ibfk_4` FOREIGN KEY (`creator_id`) REFERENCES `employee` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_product_item
-- ----------------------------
DROP TABLE IF EXISTS `order_product_item`;
CREATE TABLE `order_product_item`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL COMMENT '关联order_head.id',
  `product_id` int NOT NULL COMMENT '关联product.id',
  `user_count` int NOT NULL COMMENT '使用人数',
  `account_count` int NOT NULL COMMENT '账套数',
  `duration_months` int NOT NULL COMMENT '购买时长(月)',
  `selected_features` json NULL COMMENT '所选功能ID列表',
  `standard_price` decimal(10, 2) NOT NULL COMMENT '产品标准价',
  `discount_rate` decimal(5, 4) NULL DEFAULT 1.0000 COMMENT '折扣率',
  `other_discount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '其他优惠',
  `actual_price` decimal(10, 2) NOT NULL COMMENT '实付价格',
  `activity_other` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '其他活动说明',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id` ASC) USING BTREE,
  INDEX `product_id`(`product_id` ASC) USING BTREE,
  CONSTRAINT `order_product_item_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `order_head` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `order_product_item_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_service_item
-- ----------------------------
DROP TABLE IF EXISTS `order_service_item`;
CREATE TABLE `order_service_item`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL COMMENT '关联order_head.id',
  `service_name` enum('实施服务','售后服务','sps服务') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `standard_price` decimal(10, 2) NOT NULL COMMENT '标准价格',
  `actual_price` decimal(10, 2) NOT NULL COMMENT '实际价格',
  `asset_price_field` enum('implementation_fee','after_sales_service_fee','sps_annual_fee') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '对应资产表中的价格字段（不包括product_standard_price，因为产品价格在产品订单表体中处理）',
  `related_order_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联产品订单号',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id` ASC) USING BTREE,
  CONSTRAINT `order_service_item_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `order_head` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chk_service_price_mapping` CHECK (((`service_name` = _utf8mb4'实施服务') and (`asset_price_field` = _utf8mb4'implementation_fee')) or ((`service_name` = _utf8mb4'售后服务') and (`asset_price_field` = _utf8mb4'after_sales_service_fee')) or ((`service_name` = _utf8mb4'sps服务') and (`asset_price_field` = _utf8mb4'sps_annual_fee')))
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '产品ID（如HKJ01）',
  `product_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '产品名称',
  `version_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '版本名称',
  `base_price` decimal(10, 2) NOT NULL COMMENT '基础价格',
  `base_account_count` int NOT NULL COMMENT '基础账套数',
  `base_user_count` int NOT NULL COMMENT '基础使用人数',
  `allow_user_addon` tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许增用户',
  `allow_account_addon` tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许增账套',
  `addons` json NULL COMMENT '增购规则',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `createdAt` datetime NULL DEFAULT NULL,
  `updatedAt` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_product_id_unique`(`product_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_feature
-- ----------------------------
DROP TABLE IF EXISTS `product_feature`;
CREATE TABLE `product_feature`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `feature_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '功能ID（如FT001）',
  `feature_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '功能名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '功能描述',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_feature_id_unique`(`feature_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_feature_relation
-- ----------------------------
DROP TABLE IF EXISTS `product_feature_relation`;
CREATE TABLE `product_feature_relation`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `product_id` int NOT NULL COMMENT '关联product.id',
  `product_feature_id` int NOT NULL COMMENT '关联product_feature.id',
  `feature_price` decimal(10, 2) NOT NULL COMMENT '功能价格',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_feature_id`(`product_feature_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  CONSTRAINT `fk_pfr_to_pf` FOREIGN KEY (`product_feature_id`) REFERENCES `product_feature` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_pfr_to_product_new` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_user_addon_tier
-- ----------------------------
DROP TABLE IF EXISTS `product_user_addon_tier`;
CREATE TABLE `product_user_addon_tier`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `product_id` int NOT NULL COMMENT '关联product.id',
  `min_users` int NOT NULL COMMENT '阶梯起始人数（包含此数值）',
  `max_users` int NULL DEFAULT NULL COMMENT '阶梯结束人数（包含此数值），NULL表示无上限',
  `price_per_user` decimal(10, 2) NOT NULL COMMENT '在此阶梯区间内，每增加一个用户的单价',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注（可选），用于业务说明',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  CONSTRAINT `fk_tier_to_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '产品用户数增购阶梯价格表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID（业务ID如U202501001）',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户姓名',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `mobile` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `avatar_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像URL',
  `wechat_unionid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信unionid',
  `wechat_openid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信小程序openid',
  `login_type` enum('password','wechat','partner') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'password' COMMENT '登录方式：password-密码登录，wechat-微信登录，partner-合伙人登录',
  `is_partner` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否合伙人（0否1是）',
  `partner_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '合伙人ID（如PT0001）',
  `commission_ratio` decimal(5, 4) NULL DEFAULT NULL COMMENT '分润比例（如0.1）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_mobile`(`mobile` ASC) USING BTREE,
  UNIQUE INDEX `idx_user_id_unique`(`user_id` ASC) USING BTREE,
  UNIQUE INDEX `idx_partner_id`(`partner_id` ASC) USING BTREE,
  INDEX `idx_wechat_openid`(`wechat_openid` ASC) USING BTREE,
  INDEX `idx_external_id`(`wechat_unionid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_authentication
-- ----------------------------
DROP TABLE IF EXISTS `user_authentication`;
CREATE TABLE `user_authentication`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int NOT NULL COMMENT '关联user.id',
  `id_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '身份证号',
  `bank_card` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '银行卡号',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `fk_auth_to_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_password
-- ----------------------------
DROP TABLE IF EXISTS `user_password`;
CREATE TABLE `user_password`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int NOT NULL COMMENT '关联user.id',
  `password` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '加密密码（SHA256+盐）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `fk_pwd_to_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
