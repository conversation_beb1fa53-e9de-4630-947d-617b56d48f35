#!/bin/bash

echo "=== Nginx配置诊断脚本 ==="
echo

echo "1. 检查nginx配置语法："
sudo nginx -t
echo

echo "2. 检查sites-enabled中的链接："
ls -la /etc/nginx/sites-enabled/
echo

echo "3. 检查service.bogoo.net配置是否存在："
if [ -f "/etc/nginx/sites-enabled/service.bogoo.net" ]; then
    echo "✓ service.bogoo.net 配置已启用"
else
    echo "✗ service.bogoo.net 配置未启用"
    echo "需要创建软链接："
    echo "sudo ln -s /etc/nginx/sites-available/service.bogoo.net /etc/nginx/sites-enabled/"
fi
echo

echo "4. 检查当前生效的nginx配置："
sudo nginx -T | grep -A 30 "server_name service.bogoo.net"
echo

echo "5. 检查后端服务状态："
curl -s http://localhost:3002/ || echo "后端服务无响应"
echo

echo "6. 测试nginx代理："
echo "测试GET请求："
curl -s -o /dev/null -w "%{http_code}" https://service.bogoo.net/api/enterprises
echo

echo "测试POST请求："
curl -s -o /dev/null -w "%{http_code}" -X POST https://service.bogoo.net/api/enterprises \
  -H "Content-Type: application/json" \
  -H "Origin: https://admin.bogoo.net" \
  -d '{"name":"test"}'
echo

echo "7. 检查nginx进程："
ps aux | grep nginx
echo

echo "8. 重启nginx服务："
echo "执行: sudo systemctl restart nginx"
sudo systemctl restart nginx
echo "nginx已重启"

echo
echo "=== 诊断完成 ==="
