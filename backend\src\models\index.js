'use strict';

const Sequelize = require('sequelize');
const db = {};

// 引入我们的数据库连接实例
const sequelize = require('../config/database');

// --- 核心改动：手动、明确地引入每一个模型 ---
// 这种方式更稳定，可以避免因文件名大小写或自动加载逻辑产生的错误。
const Employee = require('./employee.model');
const User = require('./user.model');
const Enterprise = require('./enterprise.model');
const Followup = require('./followup.model');
const Product = require('./product.model');
// 1. 引入产品功能和关联关系模型
const ProductFeature = require('./product_feature.model.js');
const ProductFeatureRelation = require('./product_feature_relation.model.js');
// 1. 引入资产和资产变更记录模型
const Asset = require('./asset.model.js');
const AssetChangeLog = require('./asset_change_log.model.js');
// 引入新的订单模型
const OrderHead = require('./order_head.model');
const OrderProductItem = require('./order_product_item.model');
const OrderServiceItem = require('./order_service_item.model');
const OrderAttachment = require('./order_attachment.model');
const UserPassword = require('./user_password.model.js');
const UserAuthentication = require('./user_authentication.model.js');
// [修正] 引入我们新建的阶梯价格模型，使用下划线命名
const ProductUserAddonTier = require('./product_user_addon_tier.model.js');

// 将加载的模型存入 db 对象
db.Employee = Employee;
db.User = User;
db.Enterprise = Enterprise;
db.Followup = Followup;
db.Product = Product;
// 2. 将新模型添加到 db 对象
db.ProductFeature = ProductFeature;
db.ProductFeatureRelation = ProductFeatureRelation;
// 2. 将新模型添加到 db 对象
db.Asset = Asset;
db.AssetChangeLog = AssetChangeLog;
// 添加新的订单模型到 db 对象
db.OrderHead = OrderHead;
db.OrderProductItem = OrderProductItem;
db.OrderServiceItem = OrderServiceItem;
db.OrderAttachment = OrderAttachment;
db.UserPassword = UserPassword;
db.UserAuthentication = UserAuthentication;
// [修正] 将新模型添加到 db 对象
db.ProductUserAddonTier = ProductUserAddonTier;
// --- 改动结束 ---


// --- 这是我们定义关联关系的核心部分 ---
// 注意：现在我们可以100%确定 db.Enterprise, db.Employee, db.User 都是存在的。

// 企业(Enterprise)与员工(Employee)的关联
// 一个企业属于一个负责员工
db.Enterprise.belongsTo(db.Employee, {
  foreignKey: 'employee_id', // Enterprise表中的外键
  as: 'employee' // 查询时使用的别名
});
// 一个员工可以负责多个企业
db.Employee.hasMany(db.Enterprise, {
  foreignKey: 'employee_id'
});


// 企业(Enterprise)与用户(User)的关联
// 一个企业属于一个关联用户
db.Enterprise.belongsTo(db.User, {
  foreignKey: 'user_id', // Enterprise表中的外键
  targetKey: 'id', // [新增] 明确 User 模型的主键是 id
  as: 'user' // 查询时使用的别名
});
// 一个用户可以拥有多个企业
db.User.hasMany(db.Enterprise, {
  foreignKey: 'user_id',
  sourceKey: 'id' // [新增] 明确 User 模型的主键是 id
});

// 企业跟进(Followup)的关联关系
// 一个跟进记录属于一个企业
db.Followup.belongsTo(db.Enterprise, {
  foreignKey: 'enterprise_id',
  targetKey: 'id',
  as: 'enterprise'
});
db.Enterprise.hasMany(db.Followup, {
  foreignKey: 'enterprise_id',
  sourceKey: 'id',
  as: 'followups'
});

// 一个跟进记录属于一个员工
db.Followup.belongsTo(db.Employee, {
  foreignKey: 'employee_id',
  as: 'employee'
});
db.Employee.hasMany(db.Followup, {
  foreignKey: 'employee_id',
  as: 'followups'
});

// --- 新增：产品与产品功能的多对多关联 ---
// 一个产品 (Product) 可以拥有多个功能 (ProductFeature)
db.Product.belongsToMany(db.ProductFeature, {
  through: db.ProductFeatureRelation, // 通过我们定义的关联模型
  foreignKey: 'product_id', // 在关联表中的外键名
  sourceKey: 'id', // 明确 Product 模型的主键是 id
  otherKey: 'product_feature_id',   // 在关联表中指向目标模型(ProductFeature)的外键名 (新)
  targetKey: 'id', // 明确指定 ProductFeature 模型的主键是 id
  as: 'features' // 查询产品时，用 'features' 别名来包含功能列表
});

// 一个功能 (ProductFeature) 可以属于多个产品 (Product)
db.ProductFeature.belongsToMany(db.Product, {
  through: db.ProductFeatureRelation, // 必须通过同一个关联模型
  foreignKey: 'product_feature_id', // (新)
  sourceKey: 'id', // 明确指定 ProductFeature 模型的主键是 id
  otherKey: 'product_id',
  targetKey: 'id', // 明确指定 Product 模型的主键是 id
  as: 'products' // 查询功能时，用 'products' 别名来包含产品列表
});

// [新增] 定义 产品(Product) 与 用户增购阶梯价格(ProductUserAddonTier) 的一对多关系
// 一个产品可以拥有多条阶梯价格记录
db.Product.hasMany(db.ProductUserAddonTier, {
  foreignKey: 'product_id', // 在 ProductUserAddonTier 表中，用于关联 Product 表的外键字段
  as: 'userAddonTiers',      // 定义一个别名，之后查询产品时，可以通过这个别名轻松地同时获取其所有阶梯价格
  onDelete: 'CASCADE',      // 设置级联删除：如果一个产品被删除了，它名下所有的阶梯价格记录也会被自动删除
});
// 一条阶梯价格记录只属于一个产品
db.ProductUserAddonTier.belongsTo(db.Product, {
  foreignKey: 'product_id', // 外键字段
});


// --- 新增：资产相关的关联关系 ---

// 资产与用户 (一对多)
db.User.hasMany(db.Asset, { foreignKey: 'user_id', sourceKey: 'id', as: 'assets' });
db.Asset.belongsTo(db.User, { foreignKey: 'user_id', targetKey: 'id', as: 'user' });

// 资产与企业 (一对多)
db.Enterprise.hasMany(db.Asset, { foreignKey: 'enterprise_id', sourceKey: 'id', as: 'assets' });
db.Asset.belongsTo(db.Enterprise, { foreignKey: 'enterprise_id', targetKey: 'id', as: 'enterprise' });

// 资产与产品 (一对多)
db.Product.hasMany(db.Asset, { foreignKey: 'product_id', sourceKey: 'id', as: 'assets' });
db.Asset.belongsTo(db.Product, { foreignKey: 'product_id', targetKey: 'id', as: 'product' });

// [修复] 新增：资产与创建人(Employee)的关联
// 一个员工可以创建多个资产
db.Employee.hasMany(db.Asset, { 
  foreignKey: 'creator_id',
  as: 'createdAssets' // 别名，表示由该员工创建的所有资产
});
// 一个资产属于一个创建人
db.Asset.belongsTo(db.Employee, { 
  foreignKey: 'creator_id',
  as: 'creator' // 别名，必须为 'creator' 以匹配控制器中的调用
});

// --- 新增：资产变更记录相关的关联关系 ---

// 资产与变更记录 (一对多)
db.Asset.hasMany(db.AssetChangeLog, { foreignKey: 'asset_id', sourceKey: 'id', as: 'changeLogs' });
db.AssetChangeLog.belongsTo(db.Asset, { foreignKey: 'asset_id', targetKey: 'id', as: 'asset' });

// [修正] 员工与变更记录 (一对多) 的关联关系
// 一个员工可以创建多个变更记录
db.Employee.hasMany(db.AssetChangeLog, { 
  foreignKey: 'creator_id', // [修正] 外键为 creator_id
  as: 'createdChangeLogs' // [修正] 为 "hasMany" 关联提供一个清晰的别名
});
// 一个变更记录属于一个创建人
db.AssetChangeLog.belongsTo(db.Employee, { 
  foreignKey: 'creator_id', // [修正] 外键为 creator_id
  as: 'creator'           // [修正] 别名为 creator, 与控制器中的调用保持一致
});

// --- 新增：订单相关的关联关系 ---

// 订单表头(OrderHead) 与 创建人(Employee) 的关联
db.OrderHead.belongsTo(db.Employee, {
  foreignKey: 'creator_id',
  as: 'creator'
});

// OrderHead 与 User (many-to-one)
db.User.hasMany(db.OrderHead, { foreignKey: 'user_id', sourceKey: 'id' });
db.OrderHead.belongsTo(db.User, { foreignKey: 'user_id', targetKey: 'id', as: 'user' });

// OrderHead 与 Enterprise (many-to-one)
db.Enterprise.hasMany(db.OrderHead, { foreignKey: 'enterprise_id', sourceKey: 'id' });
db.OrderHead.belongsTo(db.Enterprise, { foreignKey: 'enterprise_id', targetKey: 'id', as: 'enterprise'});

// OrderHead 与 Asset (many-to-one)
db.Asset.hasMany(db.OrderHead, { foreignKey: 'asset_id', sourceKey: 'id' });
db.OrderHead.belongsTo(db.Asset, { foreignKey: 'asset_id', targetKey: 'id', as: 'asset' });

// OrderHead 与 OrderProductItem (one-to-many)
db.OrderHead.hasMany(db.OrderProductItem, { foreignKey: 'order_id', sourceKey: 'id', as: 'productItems' });
db.OrderProductItem.belongsTo(db.OrderHead, { foreignKey: 'order_id', targetKey: 'id', as: 'order' });

// OrderHead 与 OrderServiceItem (one-to-many)
db.OrderHead.hasMany(db.OrderServiceItem, { foreignKey: 'order_id', sourceKey: 'id', as: 'serviceItems' });
db.OrderServiceItem.belongsTo(db.OrderHead, { foreignKey: 'order_id', targetKey: 'id', as: 'order' });

// OrderProductItem 与 Product (many-to-one)
db.Product.hasMany(db.OrderProductItem, { foreignKey: 'product_id', sourceKey: 'id' });
db.OrderProductItem.belongsTo(db.Product, { foreignKey: 'product_id', targetKey: 'id', as: 'product' });

// OrderHead 与 OrderAttachment (one-to-many)
db.OrderHead.hasMany(db.OrderAttachment, { foreignKey: 'order_id', sourceKey: 'id', as: 'attachments' });
db.OrderAttachment.belongsTo(db.OrderHead, { foreignKey: 'order_id', targetKey: 'id', as: 'order' });

// 订单附件(OrderAttachment) 与 上传人(Employee) 的关联
db.OrderAttachment.belongsTo(db.Employee, {
  foreignKey: 'uploader_id',
  as: 'uploader'
});


// --- 新增：用户与密码/认证信息的关联关系 ---

// 用户与密码 (一对一)
db.User.hasOne(db.UserPassword, {
  foreignKey: 'user_id',
  sourceKey: 'id',
  as: 'passwordDetail' // 使用 passwordDetail 以避免和字段冲突
});
db.UserPassword.belongsTo(db.User, {
  foreignKey: 'user_id',
  targetKey: 'id'
});

// 用户与认证信息 (一对一)
db.User.hasOne(db.UserAuthentication, {
  foreignKey: 'user_id',
  sourceKey: 'id',
  as: 'authentication'
});
db.UserAuthentication.belongsTo(db.User, {
  foreignKey: 'user_id',
  targetKey: 'id'
});

// --- 关联关系定义结束 ---


// 将 sequelize 实例和 Sequelize 库本身也放入 db 对象，方便统一调用
db.sequelize = sequelize;
db.Sequelize = Sequelize;

module.exports = db; 