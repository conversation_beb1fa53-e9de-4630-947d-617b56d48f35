const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 订单表头模型 (order_head)
 * 统一的订单表头，支持产品订单和服务订单
 */
const OrderHead = sequelize.define('OrderHead', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '自增主键ID',
  },
  order_id: {
    type: DataTypes.STRING(20),
    unique: true,
    allowNull: false,
    comment: '订单业务ID（PO开头=产品订单，SO开头=服务订单）'
  },
  order_category: {
    type: DataTypes.ENUM('产品订单', '服务订单'),
    allowNull: false,
    comment: '订单大类'
  },
  enterprise_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '关联enterprise.id'
  },
  asset_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '关联asset.id'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'user',
      key: 'id'
    },
    comment: '关联user.id'
  },
  creation_method: {
    type: DataTypes.ENUM('手工创建', '用户创建'),
    allowNull: false,
    comment: '创建方式'
  },
  order_type: {
    type: DataTypes.ENUM('普通订单', '续费订单', '变更订单'),
    allowNull: false,
    comment: '订单类型'
  },
  standard_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '订单标准金额'
  },
  actual_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '订单实付金额'
  },
  tax_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '税额（仅产品订单使用）'
  },
  invoice_type: {
    type: DataTypes.ENUM('不开票', '普票', '专票'),
    allowNull: true,
    defaultValue: '不开票',
    comment: '发票类型（仅产品订单使用）'
  },
  payment_status: {
    type: DataTypes.ENUM('待支付', '已支付'),
    allowNull: false,
    defaultValue: '待支付',
    comment: '支付状态（仅产品订单使用）'
  },
  payment_method: {
    type: DataTypes.ENUM('在线', '对公'),
    allowNull: true,
    comment: '支付方式（仅产品订单使用）'
  },
  payment_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '支付时间（仅产品订单使用）'
  },
  is_partner_order: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否合伙人订单'
  },
  partner_id: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '合伙人ID'
  },
  commission_ratio: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: true,
    comment: '分润比例'
  },
  commission_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '分润金额'
  },
  commission_status: {
    type: DataTypes.ENUM('已发放', '未发放'),
    allowNull: true,
    defaultValue: '未发放',
    comment: '分润状态'
  },
  audit_status: {
    type: DataTypes.ENUM('待审核', '已审核', '已拒绝'),
    allowNull: false,
    defaultValue: '待审核',
    comment: '审核状态'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  },
  creator_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '制单人（关联employee.id）'
  }
}, {
  tableName: 'order_head',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  underscored: true,
  charset: 'utf8mb4',
  collate: 'utf8mb4_unicode_ci'
});

module.exports = OrderHead;