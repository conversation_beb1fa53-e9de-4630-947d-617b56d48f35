<template>
  <div class="order-attachment">
    <!-- 上传区域 -->
    <div v-if="isEditing" class="upload-section">
      <!-- 文件类型和备注选择区域 -->
      <div class="upload-options">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="文件类型">
              <el-select v-model="uploadFileType" placeholder="请选择文件类型" style="width: 100%">
                <el-option label="合同" value="合同" />
                <el-option label="发票" value="发票" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="备注">
              <el-input
                v-model="uploadRemark"
                placeholder="请输入文件备注（可选）"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 上传组件 -->
      <el-upload
        ref="uploadRef"
        :action="uploadActionUrl"
        :headers="uploadHeaders"
        :data="uploadData"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload"
        :show-file-list="false"
        multiple
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 jpg/png/gif/pdf/doc/docx/xls/xlsx 格式，单个文件不超过 10MB
          </div>
        </template>
      </el-upload>
    </div>

    <!-- 附件列表 -->
    <div class="attachment-list">
      <div class="list-header">
        <h4>附件列表</h4>
        <span class="attachment-count">共 {{ attachments.length }} 个附件</span>
      </div>

      <el-table 
        :data="attachments" 
        v-loading="loading"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        
        <!-- 文件名 -->
        <el-table-column label="文件名" min-width="200">
          <template #default="{ row }">
            <div class="file-info">
              <el-icon class="file-icon">
                <component :is="getFileIcon(row.filename)" />
              </el-icon>
              <span class="filename">{{ row.filename }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 文件类型 -->
        <el-table-column label="文件类型" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getFileTypeTagType(row.file_type)" 
              size="small"
            >
              {{ row.file_type }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 文件大小 -->
        <el-table-column label="文件大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.file_size) }}
          </template>
        </el-table-column>

        <!-- 上传人 -->
        <el-table-column label="上传人" width="100">
          <template #default="{ row }">
            {{ row.uploader?.name || 'N/A' }}
          </template>
        </el-table-column>

        <!-- 上传时间 -->
        <el-table-column label="上传时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.uploaded_at) }}
          </template>
        </el-table-column>

        <!-- 备注 -->
        <el-table-column label="备注" min-width="150">
          <template #default="{ row }">
            <el-input 
              v-if="isEditing && editingRemark === row.id"
              v-model="row.remark" 
              size="small"
              @blur="saveRemark(row)"
              @keyup.enter="saveRemark(row)"
            />
            <div v-else class="remark-display" @click="editRemark(row)">
              {{ row.remark || '点击添加备注' }}
            </div>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="handlePreview(row)"
            >
              预览
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click="handleDownload(row)"
            >
              下载
            </el-button>
            <el-button 
              v-if="isEditing"
              type="danger" 
              size="small" 
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="!loading && attachments.length === 0" class="empty-state">
        <el-empty description="暂无附件">
          <el-button v-if="isEditing" type="primary" @click="triggerUpload">
            上传第一个附件
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 文件预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="文件预览"
      width="80%"
      :before-close="closePreview"
    >
      <div class="preview-container">
        <!-- 图片预览 -->
        <img 
          v-if="previewFile && isImageFile(previewFile.filename)"
          :src="getPreviewUrl(previewFile)"
          alt="预览图片"
          class="preview-image"
        />
        
        <!-- PDF预览 -->
        <iframe 
          v-else-if="previewFile && isPdfFile(previewFile.filename)"
          :src="getPreviewUrl(previewFile)"
          class="preview-iframe"
        ></iframe>
        
        <!-- 其他文件类型 -->
        <div v-else class="preview-placeholder">
          <el-icon class="large-icon"><Document /></el-icon>
          <p>此文件类型不支持预览，请下载后查看</p>
          <el-button type="primary" @click="handleDownload(previewFile)">
            下载文件
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  UploadFilled, 
  Document, 
  Picture, 
  VideoPlay,
  Files
} from '@element-plus/icons-vue';
import { getAttachments, deleteAttachment } from '@/api/order';
import { formatDateTime } from '@/utils/format';
import { useAuth } from '@/store/auth';

// Props
const props = defineProps({
  orderId: {
    type: [String, Number],
    required: true
  },
  isEditing: {
    type: Boolean,
    default: false
  }
});

// 认证状态
const { state: authState } = useAuth();

// 响应式数据
const uploadRef = ref(null);
const loading = ref(false);
const attachments = ref([]);
const editingRemark = ref(null);
const previewDialogVisible = ref(false);
const previewFile = ref(null);
const uploadFileType = ref('其他'); // 默认文件类型
const uploadRemark = ref(''); // 上传备注

// 计算属性：上传地址
const uploadActionUrl = computed(() => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'https://service.bogoo.net/api';
  return `${baseURL}/orders/${props.orderId}/attachments`;
});

// 计算属性：上传请求头
const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${authState.token}`
  };
});

// 计算属性：上传数据
const uploadData = computed(() => {
  return {
    file_type: uploadFileType.value,
    remark: uploadRemark.value
  };
});

// 获取附件列表
const fetchAttachments = async () => {
  if (!props.orderId) return;
  
  loading.value = true;
  try {
    const response = await getAttachments(props.orderId);
    attachments.value = response || [];
  } catch (error) {
    console.error('获取附件列表失败:', error);
    ElMessage.error('获取附件列表失败');
  } finally {
    loading.value = false;
  }
};

// 监听orderId变化
watch(() => props.orderId, fetchAttachments, { immediate: true });

// 上传前检查
const beforeUpload = (file) => {
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];
  
  const isAllowedType = allowedTypes.includes(file.type);
  const isLt10M = file.size / 1024 / 1024 < 10;
  
  if (!isAllowedType) {
    ElMessage.error('不支持的文件格式！');
    return false;
  }
  
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB！');
    return false;
  }
  
  return true;
};

// 上传成功回调
const handleUploadSuccess = (response, file) => {
  ElMessage.success(`文件 "${file.name}" 上传成功`);
  // 清空表单
  uploadFileType.value = '其他';
  uploadRemark.value = '';
  fetchAttachments(); // 重新获取附件列表
};

// 上传失败回调
const handleUploadError = (error, file) => {
  console.error('文件上传失败:', error);
  ElMessage.error(`文件 "${file.name}" 上传失败`);
};

// 触发上传
const triggerUpload = () => {
  uploadRef.value.$el.querySelector('input').click();
};

// 获取文件图标
const getFileIcon = (filename) => {
  const ext = filename.split('.').pop().toLowerCase();
  
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext)) {
    return Picture;
  } else if (['mp4', 'avi', 'mov', 'wmv'].includes(ext)) {
    return VideoPlay;
  } else if (['pdf', 'doc', 'docx', 'xls', 'xlsx'].includes(ext)) {
    return Document;
  } else {
    return Files;
  }
};

// 获取文件类型标签颜色
const getFileTypeTagType = (fileType) => {
  switch (fileType) {
    case '合同':
      return 'primary';
    case '发票':
      return 'success';
    case '其他':
      return 'info';
    default:
      return 'info';
  }
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return 'N/A';
  
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  
  const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

// 编辑备注
const editRemark = (row) => {
  if (props.isEditing) {
    editingRemark.value = row.id;
  }
};

// 保存备注
const saveRemark = (row) => {
  editingRemark.value = null;
  // 这里可以调用API保存备注
  ElMessage.success('备注已保存');
};

// 预览文件
const handlePreview = (row) => {
  previewFile.value = row;
  previewDialogVisible.value = true;
};

// 关闭预览
const closePreview = () => {
  previewDialogVisible.value = false;
  previewFile.value = null;
};

// 下载文件
const handleDownload = (row) => {
  const downloadUrl = `/api/orders/${props.orderId}/attachments/${row.id}/download`;
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = row.filename;
  link.click();
};

// 删除附件
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除附件 "${row.filename}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    await deleteAttachment(props.orderId, row.id);
    ElMessage.success('删除成功');
    await fetchAttachments();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除附件失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 获取预览URL
const getPreviewUrl = (file) => {
  return `/api/orders/${props.orderId}/attachments/${file.id}/download`;
};

// 判断是否为图片文件
const isImageFile = (filename) => {
  const ext = filename.split('.').pop().toLowerCase();
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext);
};

// 判断是否为PDF文件
const isPdfFile = (filename) => {
  const ext = filename.split('.').pop().toLowerCase();
  return ext === 'pdf';
};

// 组件挂载时获取数据
onMounted(() => {
  fetchAttachments();
});
</script>

<style scoped>
.order-attachment {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.upload-section {
  margin-bottom: 20px;
}

.upload-options {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.upload-options :deep(.el-form-item) {
  margin-bottom: 0;
}

.upload-section :deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.list-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.attachment-count {
  color: #909399;
  font-size: 14px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  font-size: 16px;
  color: #409eff;
}

.filename {
  color: #303133;
}

.remark-display {
  cursor: pointer;
  color: #909399;
  min-height: 20px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.remark-display:hover {
  background-color: #f5f7fa;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 600px;
  object-fit: contain;
}

.preview-iframe {
  width: 100%;
  height: 600px;
  border: none;
}

.preview-placeholder {
  padding: 60px 20px;
}

.large-icon {
  font-size: 64px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.preview-placeholder p {
  color: #909399;
  margin-bottom: 20px;
}
</style>
