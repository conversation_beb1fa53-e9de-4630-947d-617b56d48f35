浏览器日志里的报错：
初始化新服务订单
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/next-id?orderType=service"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
Ga.<computed> @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
c @ order-B9WI0psH.js:1
L @ ReviewActions-BnrkODUX.js:1
(匿名) @ ReviewActions-BnrkODUX.js:1
(匿名) @ index-DXHWKRds.js:14
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
t.__weh.t.__weh @ index-DXHWKRds.js:14
A1 @ index-DXHWKRds.js:14
L1 @ index-DXHWKRds.js:14
Promise.then
P1 @ index-DXHWKRds.js:14
gT @ index-DXHWKRds.js:14
n$ @ index-DXHWKRds.js:14
Zv.i.scheduler @ index-DXHWKRds.js:14
d.scheduler @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
Bv @ index-DXHWKRds.js:10
notify @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
set value @ index-DXHWKRds.js:10
M @ index-DXHWKRds.js:63
(匿名) @ index-DXHWKRds.js:63
Promise.then
O @ index-DXHWKRds.js:63
w @ index-DXHWKRds.js:63
install @ index-DXHWKRds.js:63
use @ index-DXHWKRds.js:14
(匿名) @ index-DXHWKRds.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
Ga.<computed> @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
hte @ index-DXHWKRds.js:63
U @ ReviewActions-BnrkODUX.js:1
initialize @ ReviewActions-BnrkODUX.js:1
(匿名) @ ReviewActions-BnrkODUX.js:1
await in (匿名)
(匿名) @ index-DXHWKRds.js:14
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
t.__weh.t.__weh @ index-DXHWKRds.js:14
A1 @ index-DXHWKRds.js:14
L1 @ index-DXHWKRds.js:14
Promise.then
P1 @ index-DXHWKRds.js:14
gT @ index-DXHWKRds.js:14
n$ @ index-DXHWKRds.js:14
Zv.i.scheduler @ index-DXHWKRds.js:14
d.scheduler @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
Bv @ index-DXHWKRds.js:10
notify @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
set value @ index-DXHWKRds.js:10
M @ index-DXHWKRds.js:63
(匿名) @ index-DXHWKRds.js:63
Promise.then
O @ index-DXHWKRds.js:63
w @ index-DXHWKRds.js:63
install @ index-DXHWKRds.js:63
use @ index-DXHWKRds.js:14
(匿名) @ index-DXHWKRds.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
(匿名) @ index-DXHWKRds.js:18
o @ asset-BhUd3IeP.js:1
$ @ ReviewActions-BnrkODUX.js:1
initialize @ ReviewActions-BnrkODUX.js:1
(匿名) @ ReviewActions-BnrkODUX.js:1
await in (匿名)
(匿名) @ index-DXHWKRds.js:14
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
t.__weh.t.__weh @ index-DXHWKRds.js:14
A1 @ index-DXHWKRds.js:14
L1 @ index-DXHWKRds.js:14
Promise.then
P1 @ index-DXHWKRds.js:14
gT @ index-DXHWKRds.js:14
n$ @ index-DXHWKRds.js:14
Zv.i.scheduler @ index-DXHWKRds.js:14
d.scheduler @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
Bv @ index-DXHWKRds.js:10
notify @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
set value @ index-DXHWKRds.js:10
M @ index-DXHWKRds.js:63
(匿名) @ index-DXHWKRds.js:63
Promise.then
O @ index-DXHWKRds.js:63
w @ index-DXHWKRds.js:63
install @ index-DXHWKRds.js:63
use @ index-DXHWKRds.js:14
(匿名) @ index-DXHWKRds.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
Ga.<computed> @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
bte @ index-DXHWKRds.js:63
z @ ReviewActions-BnrkODUX.js:1
initialize @ ReviewActions-BnrkODUX.js:1
(匿名) @ ReviewActions-BnrkODUX.js:1
await in (匿名)
(匿名) @ index-DXHWKRds.js:14
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
t.__weh.t.__weh @ index-DXHWKRds.js:14
A1 @ index-DXHWKRds.js:14
L1 @ index-DXHWKRds.js:14
Promise.then
P1 @ index-DXHWKRds.js:14
gT @ index-DXHWKRds.js:14
n$ @ index-DXHWKRds.js:14
Zv.i.scheduler @ index-DXHWKRds.js:14
d.scheduler @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
Bv @ index-DXHWKRds.js:10
notify @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
set value @ index-DXHWKRds.js:10
M @ index-DXHWKRds.js:63
(匿名) @ index-DXHWKRds.js:63
Promise.then
O @ index-DXHWKRds.js:63
w @ index-DXHWKRds.js:63
install @ index-DXHWKRds.js:63
use @ index-DXHWKRds.js:14
(匿名) @ index-DXHWKRds.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
Ga.<computed> @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
bte @ index-DXHWKRds.js:63
z @ ReviewActions-BnrkODUX.js:1
initialize @ ReviewActions-BnrkODUX.js:1
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
JT @ index-DXHWKRds.js:14
d @ index-DXHWKRds.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
Ga.<computed> @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
hte @ index-DXHWKRds.js:63
U @ ReviewActions-BnrkODUX.js:1
initialize @ ReviewActions-BnrkODUX.js:1
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
JT @ index-DXHWKRds.js:14
d @ index-DXHWKRds.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
(匿名) @ index-DXHWKRds.js:18
o @ asset-BhUd3IeP.js:1
$ @ ReviewActions-BnrkODUX.js:1
initialize @ ReviewActions-BnrkODUX.js:1
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
JT @ index-DXHWKRds.js:14
d @ index-DXHWKRds.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
Ga.<computed> @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
hte @ index-DXHWKRds.js:63
U @ ReviewActions-BnrkODUX.js:1
searchEnterprises @ ReviewActions-BnrkODUX.js:1
Ce @ index-DXHWKRds.js:40
(匿名) @ index-DXHWKRds.js:40
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
Zv.i.call @ index-DXHWKRds.js:14
b @ index-DXHWKRds.js:10
Yi @ index-DXHWKRds.js:14
L1 @ index-DXHWKRds.js:14
Promise.then
P1 @ index-DXHWKRds.js:14
jv @ index-DXHWKRds.js:14
Zv.i.scheduler @ index-DXHWKRds.js:14
d.scheduler @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
Bv @ index-DXHWKRds.js:10
notify @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
set value @ index-DXHWKRds.js:10
kt @ index-DXHWKRds.js:40
Ge.n.<computed>.n.<computed> @ index-DXHWKRds.js:18
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
n @ index-DXHWKRds.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
Ga.<computed> @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
hte @ index-DXHWKRds.js:63
U @ ReviewActions-BnrkODUX.js:1
initialize @ ReviewActions-BnrkODUX.js:1
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
JT @ index-DXHWKRds.js:14
d @ index-DXHWKRds.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
Ga.<computed> @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
bte @ index-DXHWKRds.js:63
z @ ReviewActions-BnrkODUX.js:1
initialize @ ReviewActions-BnrkODUX.js:1
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
JT @ index-DXHWKRds.js:14
d @ index-DXHWKRds.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
(匿名) @ index-DXHWKRds.js:18
o @ asset-BhUd3IeP.js:1
$ @ ReviewActions-BnrkODUX.js:1
initialize @ ReviewActions-BnrkODUX.js:1
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
JT @ index-DXHWKRds.js:14
d @ index-DXHWKRds.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
Ga.<computed> @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
bte @ index-DXHWKRds.js:63
z @ ReviewActions-BnrkODUX.js:1
handleEnterpriseChange @ ReviewActions-BnrkODUX.js:1
H @ ReviewActions-BnrkODUX.js:1
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
JT @ index-DXHWKRds.js:14
mt @ index-DXHWKRds.js:40
ne @ index-DXHWKRds.js:40
hn @ index-DXHWKRds.js:40
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
n @ index-DXHWKRds.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
Ga.<computed> @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
hte @ index-DXHWKRds.js:63
U @ ReviewActions-BnrkODUX.js:1
searchEnterprises @ ReviewActions-BnrkODUX.js:1
Ce @ index-DXHWKRds.js:40
(匿名) @ index-DXHWKRds.js:40
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
Zv.i.call @ index-DXHWKRds.js:14
b @ index-DXHWKRds.js:10
Yi @ index-DXHWKRds.js:14
L1 @ index-DXHWKRds.js:14
Promise.then
P1 @ index-DXHWKRds.js:14
jv @ index-DXHWKRds.js:14
Zv.i.scheduler @ index-DXHWKRds.js:14
d.scheduler @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
Bv @ index-DXHWKRds.js:10
notify @ index-DXHWKRds.js:10
trigger @ index-DXHWKRds.js:10
set value @ index-DXHWKRds.js:10
kt @ index-DXHWKRds.js:40
Ge.n.<computed>.n.<computed> @ index-DXHWKRds.js:18
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
n @ index-DXHWKRds.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets?enterprise_id=9"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
(匿名) @ index-DXHWKRds.js:18
o @ asset-BhUd3IeP.js:1
$ @ ReviewActions-BnrkODUX.js:1
handleEnterpriseChange @ ReviewActions-BnrkODUX.js:1
H @ ReviewActions-BnrkODUX.js:1
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
JT @ index-DXHWKRds.js:14
mt @ index-DXHWKRds.js:40
ye @ index-DXHWKRds.js:40
m @ index-DXHWKRds.js:40
Ge.n.<computed>.n.<computed> @ index-DXHWKRds.js:18
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
n @ index-DXHWKRds.js:18
inspector.js:7   POST https://service.bogoo.net/api/orders/service 500 (Internal Server Error)
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-DXHWKRds.js:20
xhr @ index-DXHWKRds.js:20
Xg @ index-DXHWKRds.js:22
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
(匿名) @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
g @ order-B9WI0psH.js:1
k @ ServiceOrderForm-PTEgF9qe.js:1
await in k
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
JT @ index-DXHWKRds.js:14
handleClick @ index-DXHWKRds.js:23
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
n @ index-DXHWKRds.js:18
index-DXHWKRds.js:59  API Error:  xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
(匿名) @ index-DXHWKRds.js:59
Promise.then
_request @ index-DXHWKRds.js:23
request @ index-DXHWKRds.js:22
(匿名) @ index-DXHWKRds.js:23
(匿名) @ index-DXHWKRds.js:18
g @ order-B9WI0psH.js:1
k @ ServiceOrderForm-PTEgF9qe.js:1
await in k
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
JT @ index-DXHWKRds.js:14
handleClick @ index-DXHWKRds.js:23
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
n @ index-DXHWKRds.js:18
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
ServiceOrderForm-PTEgF9qe.js:1  保存失败: xt {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
k @ ServiceOrderForm-PTEgF9qe.js:1
await in k
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
JT @ index-DXHWKRds.js:14
handleClick @ index-DXHWKRds.js:23
Yi @ index-DXHWKRds.js:14
Wo @ index-DXHWKRds.js:14
n @ index-DXHWKRds.js:18
inspector.js:7 XHR 加载失败:POST "https://service.bogoo.net/api/orders/service".


后端终端报错：
 OUTER JOIN `enterprise` AS `enterprise` ON `OrderHead`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `asset` AS `asset` ON `OrderHead`.`asset_id` = `asset`.`id` LEFT OUTER JOIN `product` AS `asset->product` ON `asset`.`product_id` = `asset->product`.`id` LEFT OUTER JOIN `order_product_item` AS `productItems` ON `OrderHead`.`id` = `productItems`.`order_id` LEFT OUTER JOIN `product` AS `productItems->product` ON `productItems`.`product_id` = `productItems->product`.`id` LEFT OUTER JOIN `order_service_item` AS `serviceItems` ON `OrderHead`.`id` = `serviceItems`.`order_id` LEFT OUTER JOIN `order_attachment` AS `attachments` ON `OrderHead`.`id` = `attachments`.`order_id` LEFT OUTER JOIN `employee` AS `attachments->uploader` ON `attachments`.`uploader_id` = `attachments->uploader`.`id` LEFT OUTER JOIN `employee` AS `creator` ON `OrderHead`.`creator_id` = `creator`.`id` WHERE `OrderHead`.`id` = 6;
1|customer | Executing (a6d10647-609d-4042-9fa6-f614e59cbdc8): ROLLBACK;

/root/.pm2/logs/customer-backend-error.log last 150 lines:
1|customer | 获取企业关联订单时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getOrdersByEnterprise (/var/www/customer_system/backend/src/controllers/enterprise.controller.js:313:36)
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:70:5
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:47:5
1|customer |     at /var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:261:12
1|customer |     at getSecret (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:97:14)
1|customer |     at module.exports [as verify] (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:101:10)
1|customer | 获取变更记录详情时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getAssetChangeById (/var/www/customer_system/backend/src/controllers/asset.controller.js:434:43)
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer | 获取企业关联订单时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getOrdersByEnterprise (/var/www/customer_system/backend/src/controllers/enterprise.controller.js:313:36)
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:70:5
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:47:5
1|customer |     at /var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:261:12
1|customer |     at getSecret (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:97:14)
1|customer |     at module.exports [as verify] (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:101:10)
1|customer | 获取企业关联订单时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getOrdersByEnterprise (/var/www/customer_system/backend/src/controllers/enterprise.controller.js:313:36)
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:70:5
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:47:5
1|customer |     at /var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:261:12
1|customer |     at getSecret (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:97:14)
1|customer |     at module.exports [as verify] (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:101:10)
1|customer | 获取企业关联订单时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getOrdersByEnterprise (/var/www/customer_system/backend/src/controllers/enterprise.controller.js:313:36)
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:70:5
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:47:5
1|customer |     at /var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:261:12
1|customer |     at getSecret (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:97:14)
1|customer |     at module.exports [as verify] (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:101:10)
1|customer | 获取企业关联订单时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getOrdersByEnterprise (/var/www/customer_system/backend/src/controllers/enterprise.controller.js:313:36)
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:70:5
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:47:5
1|customer |     at /var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:261:12
1|customer |     at getSecret (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:97:14)
1|customer |     at module.exports [as verify] (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:101:10)
1|customer | 获取变更记录详情时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getAssetChangeById (/var/www/customer_system/backend/src/controllers/asset.controller.js:434:43)
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer | 获取变更记录详情时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getAssetChangeById (/var/www/customer_system/backend/src/controllers/asset.controller.js:434:43)
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer | 获取变更记录详情时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getAssetChangeById (/var/www/customer_system/backend/src/controllers/asset.controller.js:434:43)
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer | 获取变更记录详情时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getAssetChangeById (/var/www/customer_system/backend/src/controllers/asset.controller.js:434:43)
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer | 获取企业关联订单时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getOrdersByEnterprise (/var/www/customer_system/backend/src/controllers/enterprise.controller.js:313:36)
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:70:5
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:47:5
1|customer |     at /var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:261:12
1|customer |     at getSecret (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:97:14)
1|customer |     at module.exports [as verify] (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:101:10)
1|customer | 获取企业关联订单时出错: TypeError: Cannot read properties of undefined (reading 'findAll')
1|customer |     at exports.getOrdersByEnterprise (/var/www/customer_system/backend/src/controllers/enterprise.controller.js:313:36)
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:70:5
1|customer |     at Layer.handleRequest (/var/www/customer_system/backend/node_modules/router/lib/layer.js:152:17)
1|customer |     at next (/var/www/customer_system/backend/node_modules/router/lib/route.js:157:13)
1|customer |     at /var/www/customer_system/backend/src/middleware/auth/base.js:47:5
1|customer |     at /var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:261:12
1|customer |     at getSecret (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:97:14)
1|customer |     at module.exports [as verify] (/var/www/customer_system/backend/node_modules/jsonwebtoken/verify.js:101:10)
1|customer | 创建产品订单失败: Error: 创建订单失败：必须关联一个用户或一个企业。
1|customer |     at /var/www/customer_system/backend/src/services/order.service.js:101:19
1|customer |     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:507:24
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer |     at async exports.createProductOrder (/var/www/customer_system/backend/src/controllers/order.controller.js:82:26)
1|customer | 创建产品订单失败: Error: 创建订单失败：必须关联一个用户或一个企业。
1|customer |     at /var/www/customer_system/backend/src/services/order.service.js:101:19
1|customer |     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:507:24
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer |     at async exports.createProductOrder (/var/www/customer_system/backend/src/controllers/order.controller.js:82:26)
1|customer | 创建产品订单失败: Error: 创建订单失败：必须关联一个用户或一个企业。
1|customer |     at /var/www/customer_system/backend/src/services/order.service.js:101:19
1|customer |     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:507:24
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer |     at async exports.createProductOrder (/var/www/customer_system/backend/src/controllers/order.controller.js:82:26)
1|customer | 创建产品订单失败: Error: 创建订单失败：必须关联一个用户或一个企业。
1|customer |     at /var/www/customer_system/backend/src/services/order.service.js:101:19
1|customer |     at /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:507:24
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer |     at async exports.createProductOrder (/var/www/customer_system/backend/src/controllers/order.controller.js:82:26)
1|customer | 创建产品订单失败: Error: 订单未找到
1|customer |     at getOrderById (/var/www/customer_system/backend/src/services/order.service.js:80:14)
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer |     at async /var/www/customer_system/backend/src/services/order.service.js:131:16
1|customer |     at async /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:507:18
1|customer |     at async exports.createProductOrder (/var/www/customer_system/backend/src/controllers/order.controller.js:82:26)
1|customer | 创建产品订单失败: Error: 订单未找到
1|customer |     at getOrderById (/var/www/customer_system/backend/src/services/order.service.js:80:14)
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer |     at async /var/www/customer_system/backend/src/services/order.service.js:131:16
1|customer |     at async /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:507:18
1|customer |     at async exports.createProductOrder (/var/www/customer_system/backend/src/controllers/order.controller.js:82:26)
1|customer | JWT 验证失败: jwt expired
1|customer | JWT 验证失败: jwt expired
1|customer | 创建服务订单失败: Error: 订单未找到
1|customer |     at getOrderById (/var/www/customer_system/backend/src/services/order.service.js:80:14)
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer |     at async /var/www/customer_system/backend/src/services/order.service.js:181:16
1|customer |     at async /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:507:18
1|customer |     at async exports.createServiceOrder (/var/www/customer_system/backend/src/controllers/order.controller.js:101:26)
1|customer | 创建服务订单失败: Error: 订单未找到
1|customer |     at getOrderById (/var/www/customer_system/backend/src/services/order.service.js:80:14)
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer |     at async /var/www/customer_system/backend/src/services/order.service.js:181:16
1|customer |     at async /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:507:18
1|customer |     at async exports.createServiceOrder (/var/www/customer_system/backend/src/controllers/order.controller.js:101:26)
1|customer | 创建服务订单失败: Error: 订单未找到
1|customer |     at getOrderById (/var/www/customer_system/backend/src/services/order.service.js:80:14)
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer |     at async /var/www/customer_system/backend/src/services/order.service.js:181:16
1|customer |     at async /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:507:18
1|customer |     at async exports.createServiceOrder (/var/www/customer_system/backend/src/controllers/order.controller.js:101:26)
1|customer | 创建服务订单失败: Error: 订单未找到
1|customer |     at getOrderById (/var/www/customer_system/backend/src/services/order.service.js:80:14)
1|customer |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1|customer |     at async /var/www/customer_system/backend/src/services/order.service.js:181:16
1|customer |     at async /var/www/customer_system/backend/node_modules/sequelize/lib/sequelize.js:507:18
1|customer |     at async exports.createServiceOrder (/var/www/customer_system/backend/src/controllers/order.controller.js:101:26)