inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
bte @ index-CVl3lHbF.js:63
$ @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
install @ index-CVl3lHbF.js:63
use @ index-CVl3lHbF.js:14
(匿名) @ index-CVl3lHbF.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/employees"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
CE @ index-CVl3lHbF.js:63
$ @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
install @ index-CVl3lHbF.js:63
use @ index-CVl3lHbF.js:14
(匿名) @ index-CVl3lHbF.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
hte @ index-CVl3lHbF.js:63
f @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
install @ index-CVl3lHbF.js:63
use @ index-CVl3lHbF.js:14
(匿名) @ index-CVl3lHbF.js:63
enterprises:1  Access to XMLHttpRequest at 'https://service.bogoo.net/api/enterprises/9' from origin 'https://admin.bogoo.net' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
index-CVl3lHbF.js:59  API Error:  xt {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
(匿名) @ index-CVl3lHbF.js:59
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
gte @ index-CVl3lHbF.js:63
W @ index-CVl3lHbF.js:63
await in W
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
inspector.js:7   PUT https://service.bogoo.net/api/enterprises/9 net::ERR_FAILED 502 (Bad Gateway)
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
gte @ index-CVl3lHbF.js:63
W @ index-CVl3lHbF.js:63
await in W
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 加载失败:PUT "https://service.bogoo.net/api/enterprises/9".
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
gte @ index-CVl3lHbF.js:63
W @ index-CVl3lHbF.js:63
await in W
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/employees"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
CE @ index-CVl3lHbF.js:63
m @ index-CVl3lHbF.js:63
fe.immediate @ index-CVl3lHbF.js:63
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
Zv.i.call @ index-CVl3lHbF.js:14
b @ index-CVl3lHbF.js:10
vT @ index-CVl3lHbF.js:10
Zv @ index-CVl3lHbF.js:14
fe @ index-CVl3lHbF.js:14
setup @ index-CVl3lHbF.js:63
Yi @ index-CVl3lHbF.js:14
u$ @ index-CVl3lHbF.js:14
i$ @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
x @ index-CVl3lHbF.js:14
M @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
runIfDirty @ index-CVl3lHbF.js:10
Yi @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
jv @ index-CVl3lHbF.js:14
P.$e.scheduler @ index-CVl3lHbF.js:14
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
G @ index-CVl3lHbF.js:63
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/followups/enterprise/9"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
wte @ index-CVl3lHbF.js:63
p @ index-CVl3lHbF.js:63
fe.immediate @ index-CVl3lHbF.js:63
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
Zv.i.call @ index-CVl3lHbF.js:14
b @ index-CVl3lHbF.js:10
vT @ index-CVl3lHbF.js:10
Zv @ index-CVl3lHbF.js:14
fe @ index-CVl3lHbF.js:14
setup @ index-CVl3lHbF.js:63
Yi @ index-CVl3lHbF.js:14
u$ @ index-CVl3lHbF.js:14
i$ @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
x @ index-CVl3lHbF.js:14
M @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
runIfDirty @ index-CVl3lHbF.js:10
Yi @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
jv @ index-CVl3lHbF.js:14
P.$e.scheduler @ index-CVl3lHbF.js:14
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
G @ index-CVl3lHbF.js:63
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
enterprises:1  Access to XMLHttpRequest at 'https://service.bogoo.net/api/followups' from origin 'https://admin.bogoo.net' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
index-CVl3lHbF.js:59  API Error:  xt {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
(匿名) @ index-CVl3lHbF.js:59
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
Cte @ index-CVl3lHbF.js:63
g @ index-CVl3lHbF.js:63
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7   POST https://service.bogoo.net/api/followups net::ERR_FAILED 502 (Bad Gateway)
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
Cte @ index-CVl3lHbF.js:63
g @ index-CVl3lHbF.js:63
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 加载失败:POST "https://service.bogoo.net/api/followups".
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
Cte @ index-CVl3lHbF.js:63
g @ index-CVl3lHbF.js:63
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ProductOrderForm-cO49gqDQ.js:1 初始化新产品订单
ReviewActions-Dy-ctK2I.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: null, user_id: null, formData: Proxy(Object)}
ProductOrderForm-cO49gqDQ.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-cO49gqDQ.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-cO49gqDQ.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-cO49gqDQ.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: 'PO202507282XT', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: 'PO202507282XT', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: 'PO202507282XT', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/next-id?orderType=product"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
c @ order-DWor6qrj.js:1
I @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
$ @ product-EgvQ-d_C.js:1
Y @ ProductOrderForm-cO49gqDQ.js:1
(匿名) @ ProductOrderForm-cO49gqDQ.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
hte @ index-CVl3lHbF.js:63
$ @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
bte @ index-CVl3lHbF.js:63
O @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:18
o @ asset-BiPl0xug.js:1
S @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/review"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
o @ order-DWor6qrj.js:1
y @ OrderReviewList-CSxUVyc7.js:1
(匿名) @ OrderReviewList-CSxUVyc7.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:40
p @ index-CVl3lHbF.js:40
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ServiceOrderForm-CMeBMclT.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ServiceOrderForm-CMeBMclT.js:1 服务明细数据: Proxy(Array) {}
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {service_items: Proxy(Array), standard_amount: 0, actual_amount: 0}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ServiceOrderForm-CMeBMclT.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {service_items: Proxy(Array), standard_amount: 0, actual_amount: 0}
ServiceOrderForm-CMeBMclT.js:1 服务明细数据: Proxy(Array) {0: {…}}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/16/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
m @ order-DWor6qrj.js:1
O @ ReviewActions-Dy-ctK2I.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
Zv.i.call @ index-CVl3lHbF.js:14
b @ index-CVl3lHbF.js:10
vT @ index-CVl3lHbF.js:10
Zv @ index-CVl3lHbF.js:14
fe @ index-CVl3lHbF.js:14
setup @ ReviewActions-Dy-ctK2I.js:1
Yi @ index-CVl3lHbF.js:14
u$ @ index-CVl3lHbF.js:14
i$ @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
M @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
P @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
M @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
P @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
$ @ index-CVl3lHbF.js:14
_ @ index-CVl3lHbF.js:14
S @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
P @ index-CVl3lHbF.js:14
G @ index-CVl3lHbF.js:14
W @ index-CVl3lHbF.js:14
h @ index-CVl3lHbF.js:14
ve @ index-CVl3lHbF.js:14
run @ index-CVl3lHbF.js:10
runIfDirty @ index-CVl3lHbF.js:10
Yi @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
ServiceOrderForm-CMeBMclT.js:1 获取到的服务订单数据: {id: 16, order_id: 'SO20250728IY7', order_category: '服务订单', enterprise_id: 9, asset_id: 1, …}
ServiceOrderForm-CMeBMclT.js:1 服务明细数据: [{…}]
ReviewActions-Dy-ctK2I.js:1 Props更新，接收到的数据: Proxy(Object) {service_items: Array(1), standard_amount: 1000, actual_amount: 1000, id: 16, order_id: 'SO20250728IY7', …}
ReviewActions-Dy-ctK2I.js:1 当前formData: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ReviewActions-Dy-ctK2I.js:1 合并后的formData: Proxy(Object) {order_id: 'SO20250728IY7', order_category: '服务订单', enterprise_id: 9, asset_id: 1, user_id: 1, …}
ReviewActions-Dy-ctK2I.js:1 监听到 composable 选择变化: {enterpriseId: 9, userId: 1, assetId: 1, currentFormData: {…}}
ServiceOrderForm-CMeBMclT.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {service_items: Array(1), standard_amount: 1000, actual_amount: 1000, id: 16, order_id: 'SO20250728IY7', …}
ServiceOrderForm-CMeBMclT.js:1 服务明细数据: Proxy(Array) {0: {…}}
ReviewActions-Dy-ctK2I.js:1 企业字段验证: {value: 9, enterpriseId: 9, userId: 1, enterpriseType: 'number', userType: 'number'}
ReviewActions-Dy-ctK2I.js:1 用户字段验证: {value: 1, userId: 1, enterpriseId: 9, userType: 'number', enterpriseType: 'number'}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/16/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
m @ order-DWor6qrj.js:1
O @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/16"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
i @ order-DWor6qrj.js:1
n @ ServiceOrderForm-CMeBMclT.js:1
(匿名) @ ServiceOrderForm-CMeBMclT.js:1
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
Ga.<computed> @ index-CVl3lHbF.js:23
(匿名) @ index-CVl3lHbF.js:18
hte @ index-CVl3lHbF.js:63
$ @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CVl3lHbF.js:20
xhr @ index-CVl3lHbF.js:20
Xg @ index-CVl3lHbF.js:22
Promise.then
_request @ index-CVl3lHbF.js:23
request @ index-CVl3lHbF.js:22
(匿名) @ index-CVl3lHbF.js:18
o @ asset-BiPl0xug.js:1
S @ ReviewActions-Dy-ctK2I.js:1
initialize @ ReviewActions-Dy-ctK2I.js:1
(匿名) @ ReviewActions-Dy-ctK2I.js:1
await in (匿名)
(匿名) @ index-CVl3lHbF.js:14
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
t.__weh.t.__weh @ index-CVl3lHbF.js:14
A1 @ index-CVl3lHbF.js:14
L1 @ index-CVl3lHbF.js:14
Promise.then
P1 @ index-CVl3lHbF.js:14
gT @ index-CVl3lHbF.js:14
n$ @ index-CVl3lHbF.js:14
Zv.i.scheduler @ index-CVl3lHbF.js:14
d.scheduler @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
Bv @ index-CVl3lHbF.js:10
notify @ index-CVl3lHbF.js:10
trigger @ index-CVl3lHbF.js:10
set value @ index-CVl3lHbF.js:10
M @ index-CVl3lHbF.js:63
(匿名) @ index-CVl3lHbF.js:63
Promise.then
O @ index-CVl3lHbF.js:63
w @ index-CVl3lHbF.js:63
E @ OrderReviewList-CSxUVyc7.js:1
onClick @ OrderReviewList-CSxUVyc7.js:1
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
JT @ index-CVl3lHbF.js:14
handleClick @ index-CVl3lHbF.js:23
Yi @ index-CVl3lHbF.js:14
Wo @ index-CVl3lHbF.js:14
n @ index-CVl3lHbF.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。  



root@iZ7xvczjzdx1cgmypn8ufmZ:~# pm2 restart customer-backend
Use --update-env to update environment variables
[PM2] Applying action restartProcessId on app [customer-backend](ids: [ 1 ])
[PM2] [customer-backend](1) ✓
┌────┬─────────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name                │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼─────────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 1  │ customer-backend    │ default     │ 1.0.0   │ fork    │ 600217   │ 0s     │ 1148 │ online    │ 0%       │ 19.3mb   │ root     │ enabled  │
│ 3  │ partner-admin       │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
│ 2  │ partner-server      │ default     │ N/A     │ fork    │ 0        │ 0      │ 15   │ errored   │ 0%       │ 0b       │ root     │ disabled │
└────┴─────────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
root@iZ7xvczjzdx1cgmypn8ufmZ:~# cd /var/www/customer_system/frontend
# 2. 构建生产版本
npm run build

> frontend@0.0.0 build
> vite build

vite v7.0.0 building for production...
✓ 1571 modules transformed.
dist/index.html                                   0.43 kB │ gzip:   0.28 kB
dist/assets/FeatureView-tn0RQdqM.css              0.00 kB │ gzip:   0.02 kB
dist/assets/EmployeeView-B1DNsvtG.css             0.10 kB │ gzip:   0.09 kB
dist/assets/UserView-BkG48t1b.css                 0.10 kB │ gzip:   0.09 kB
dist/assets/RollbackDialog-CTPJZny2.css           0.34 kB │ gzip:   0.21 kB
dist/assets/ProductView-0JKSP_bM.css              0.35 kB │ gzip:   0.20 kB
dist/assets/AssetRelatedOrders-DwQccPfH.css       0.84 kB │ gzip:   0.39 kB
dist/assets/OrderReviewList-BLgV7eHF.css          1.00 kB │ gzip:   0.39 kB
dist/assets/ProductOrderList-C76MSe_l.css         1.30 kB │ gzip:   0.45 kB
dist/assets/ServiceOrderList-Bzf_PdFV.css         1.52 kB │ gzip:   0.48 kB
dist/assets/AssetChangeCreate-dUxPyNlI.css        1.75 kB │ gzip:   0.59 kB
dist/assets/AssetForm-JJF2viyQ.css                1.84 kB │ gzip:   0.60 kB
dist/assets/AssetChangeDetail-weBNV8-E.css        1.97 kB │ gzip:   0.64 kB
dist/assets/AssetChangeList-zXx7eJQ4.css          2.32 kB │ gzip:   0.73 kB
dist/assets/ServiceOrderForm-BvhJNDe2.css         2.53 kB │ gzip:   0.71 kB
dist/assets/ReviewActions-CGTC07k_.css            3.16 kB │ gzip:   0.90 kB
dist/assets/AssetDetail-6pXA1VrD.css              3.28 kB │ gzip:   0.95 kB
dist/assets/AssetList-Bbpm8H9c.css                3.80 kB │ gzip:   0.96 kB
dist/assets/AssetChangeCompare-BX4thpZu.css       4.18 kB │ gzip:   1.17 kB
dist/assets/ProductOrderForm-D-gaTMsU.css         4.22 kB │ gzip:   1.12 kB
dist/assets/AssetActivationInfo-CO1Cnnjp.css      5.91 kB │ gzip:   1.29 kB
dist/assets/index-C47AUgr2.css                  338.11 kB │ gzip:  46.82 kB
dist/assets/feature-Db43IMd2.js                   0.22 kB │ gzip:   0.18 kB
dist/assets/product-EgvQ-d_C.js                   0.60 kB │ gzip:   0.33 kB
dist/assets/order-DWor6qrj.js                     0.67 kB │ gzip:   0.34 kB
dist/assets/asset-BiPl0xug.js                     0.91 kB │ gzip:   0.32 kB
dist/assets/RollbackDialog-cJRNLo_o.js            2.01 kB │ gzip:   1.14 kB
dist/assets/useAssetData-D7E3LXkH.js              2.61 kB │ gzip:   1.14 kB
dist/assets/AssetChangeCreate-BSKQFueR.js         4.45 kB │ gzip:   2.04 kB
dist/assets/FeatureView-CPjuRSI-.js               4.51 kB │ gzip:   1.93 kB
dist/assets/AssetChangeDetail-Bcx5JBCL.js         4.66 kB │ gzip:   2.18 kB
dist/assets/AssetRelatedOrders-BWE6z6y6.js        5.70 kB │ gzip:   2.29 kB
dist/assets/EmployeeView-Bj22qZXM.js              6.32 kB │ gzip:   2.53 kB
dist/assets/AssetForm-CM0yY4hg.js                 6.33 kB │ gzip:   2.57 kB
dist/assets/AssetChangeList-CuZ1XJ5Q.js           6.96 kB │ gzip:   2.78 kB
dist/assets/OrderReviewList-CSxUVyc7.js           7.97 kB │ gzip:   2.77 kB
dist/assets/AssetChangeCompare-Bzmcp1tl.js        7.99 kB │ gzip:   3.05 kB
dist/assets/AssetList-CSeKpgnJ.js                 8.28 kB │ gzip:   3.25 kB
dist/assets/ServiceOrderList-Ci2U7WDD.js          9.39 kB │ gzip:   3.10 kB
dist/assets/ProductOrderList-UMDiXq6s.js          9.44 kB │ gzip:   3.06 kB
dist/assets/UserView-DFWMOMkv.js                  9.63 kB │ gzip:   3.39 kB
dist/assets/AssetDetail-CfRDjlPn.js              11.10 kB │ gzip:   4.27 kB
dist/assets/ServiceOrderForm-CMeBMclT.js         13.53 kB │ gzip:   4.76 kB
dist/assets/AssetActivationInfo-C7tUcjkQ.js      14.69 kB │ gzip:   3.86 kB
dist/assets/ProductOrderForm-cO49gqDQ.js         17.85 kB │ gzip:   5.80 kB
dist/assets/ProductView-Bh6wVLpX.js              18.38 kB │ gzip:   5.80 kB
dist/assets/ReviewActions-Dy-ctK2I.js            28.77 kB │ gzip:   8.71 kB
dist/assets/index-CVl3lHbF.js                 1,036.27 kB │ gzip: 342.92 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 11.49s
root@iZ7xvczjzdx1cgmypn8ufmZ:/var/www/customer_system/frontend# pm2 logs customer-backend --lines 50
[TAILING] Tailing last 50 lines for [customer-backend] process (change the value with --lines option)
/root/.pm2/logs/customer-backend-error.log last 50 lines:
1|customer |       14,                    'SO202507288U9',
1|customer |       '服务订单',            9,
1|customer |       1,                     1,
1|customer |       '手工创建',            '普通订单',
1|customer |       1000,                  1000,
1|customer |       null,                  '不开票',
1|customer |       '待支付',              '在线',
1|customer |       null,                  false,
1|customer |       null,                  null,
1|customer |       null,                  '未发放',
1|customer |       '待审核',              '',
1|customer |       2,                     '2025-07-28 07:40:12',
1|customer |       '2025-07-28 08:08:15'
1|customer |     ]
1|customer |   },
1|customer |   original: Error: Duplicate entry '14' for key 'order_head.PRIMARY'
1|customer |       at Packet.asError (/var/www/customer_system/backend/node_modules/mysql2/lib/packets/packet.js:740:17)
1|customer |       at Execute.execute (/var/www/customer_system/backend/node_modules/mysql2/lib/commands/command.js:29:26)
1|customer |       at Connection.handlePacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:475:34)
1|customer |       at PacketParser.onPacket (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:93:12)
1|customer |       at PacketParser.executeStart (/var/www/customer_system/backend/node_modules/mysql2/lib/packet_parser.js:75:16)
1|customer |       at Socket.<anonymous> (/var/www/customer_system/backend/node_modules/mysql2/lib/base/connection.js:100:25)
1|customer |       at Socket.emit (node:events:524:28)
1|customer |       at addChunk (node:internal/streams/readable:561:12)
1|customer |       at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
1|customer |       at Readable.push (node:internal/streams/readable:392:5) {
1|customer |     code: 'ER_DUP_ENTRY',
1|customer |     errno: 1062,
1|customer |     sqlState: '23000',
1|customer |     sqlMessage: "Duplicate entry '14' for key 'order_head.PRIMARY'",
1|customer |     sql: 'INSERT INTO `order_head` (`id`,`order_id`,`order_category`,`enterprise_id`,`asset_id`,`user_id`,`creation_method`,`order_type`,`standard_amount`,`actual_amount`,`tax_amount`,`invoice_type`,`payment_status`,`payment_method`,`payment_time`,`is_partner_order`,`partner_id`,`commission_ratio`,`commission_amount`,`commission_status`,`audit_status`,`remark`,`creator_id`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);',
1|customer |     parameters: [
1|customer |       14,                    'SO202507288U9',
1|customer |       '服务订单',            9,
1|customer |       1,                     1,
1|customer |       '手工创建',            '普通订单',
1|customer |       1000,                  1000,
1|customer |       null,                  '不开票',
1|customer |       '待支付',              '在线',
1|customer |       null,                  false,
1|customer |       null,                  null,
1|customer |       null,                  '未发放',
1|customer |       '待审核',              '',
1|customer |       2,                     '2025-07-28 07:40:12',
1|customer |       '2025-07-28 08:08:15'
1|customer |     ]
1|customer |   },
1|customer |   fields: { PRIMARY: '14' },
1|customer |   sql: 'INSERT INTO `order_head` (`id`,`order_id`,`order_category`,`enterprise_id`,`asset_id`,`user_id`,`creation_method`,`order_type`,`standard_amount`,`actual_amount`,`tax_amount`,`invoice_type`,`payment_status`,`payment_method`,`payment_time`,`is_partner_order`,`partner_id`,`commission_ratio`,`commission_amount`,`commission_status`,`audit_status`,`remark`,`creator_id`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);'
1|customer | }

/root/.pm2/logs/customer-backend-out.log last 50 lines:
1|customer | roductItems->product`.`addons` AS `productItems.product.addons`, `productItems->product`.`remark` AS `productItems.product.remark`, `productItems->product`.`createdAt` AS `productItems.product.createdAt`, `productItems->product`.`updatedAt` AS `productItems.product.updatedAt`, `serviceItems`.`id` AS `serviceItems.id`, `serviceItems`.`order_id` AS `serviceItems.order_id`, `serviceItems`.`service_name` AS `serviceItems.service_name`, `serviceItems`.`standard_price` AS `serviceItems.standard_price`, `serviceItems`.`actual_price` AS `serviceItems.actual_price`, `serviceItems`.`asset_price_field` AS `serviceItems.asset_price_field`, `serviceItems`.`related_order_id` AS `serviceItems.related_order_id`, `serviceItems`.`remark` AS `serviceItems.remark`, `attachments`.`id` AS `attachments.id`, `attachments`.`order_id` AS `attachments.order_id`, `attachments`.`filename` AS `attachments.filename`, `attachments`.`file_size` AS `attachments.file_size`, `attachments`.`file_type` AS `attachments.file_type`, `attachments`.`file_path` AS `attachments.file_path`, `attachments`.`uploader_id` AS `attachments.uploader_id`, `attachments`.`remark` AS `attachments.remark`, `attachments`.`uploaded_at` AS `attachments.uploaded_at`, `attachments->uploader`.`id` AS `attachments.uploader.id`, `attachments->uploader`.`name` AS `attachments.uploader.name`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark` FROM `order_head` AS `OrderHead` LEFT OUTER JOIN `user` AS `user` ON `OrderHead`.`user_id` = `user`.`id` LEFT OUTER JOIN `enterprise` AS `enterprise` ON `OrderHead`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `asset` AS `asset` ON `OrderHead`.`asset_id` = `asset`.`id` LEFT OUTER JOIN `product` AS `asset->product` ON `asset`.`product_id` = `asset->product`.`id` LEFT OUTER JOIN `order_product_item` AS `productItems` ON `OrderHead`.`id` = `productItems`.`order_id` LEFT OUTER JOIN `product` AS `productItems->product` ON `productItems`.`product_id` = `productItems->product`.`id` LEFT OUTER JOIN `order_service_item` AS `serviceItems` ON `OrderHead`.`id` = `serviceItems`.`order_id` LEFT OUTER JOIN `order_attachment` AS `attachments` ON `OrderHead`.`id` = `attachments`.`order_id` LEFT OUTER JOIN `employee` AS `attachments->uploader` ON `attachments`.`uploader_id` = `attachments->uploader`.`id` LEFT OUTER JOIN `employee` AS `creator` ON `OrderHead`.`creator_id` = `creator`.`id` WHERE `OrderHead`.`id` = '17';
1|customer | Executing (default): SELECT `OrderAttachment`.`id`, `OrderAttachment`.`order_id`, `OrderAttachment`.`filename`, `OrderAttachment`.`file_size`, `OrderAttachment`.`file_type`, `OrderAttachment`.`file_path`, `OrderAttachment`.`uploader_id`, `OrderAttachment`.`remark`, `OrderAttachment`.`uploaded_at`, `uploader`.`id` AS `uploader.id`, `uploader`.`name` AS `uploader.name` FROM `order_attachment` AS `OrderAttachment` LEFT OUTER JOIN `employee` AS `uploader` ON `OrderAttachment`.`uploader_id` = `uploader`.`id` WHERE `OrderAttachment`.`order_id` = '17' ORDER BY `OrderAttachment`.`uploaded_at` DESC;
1|customer | Executing (default): SELECT `Enterprise`.`id`, `Enterprise`.`enterprise_id`, `Enterprise`.`name`, `Enterprise`.`tax_number`, `Enterprise`.`bank_name`, `Enterprise`.`bank_account`, `Enterprise`.`invoice_type`, `Enterprise`.`contact_person`, `Enterprise`.`contact_phone`, `Enterprise`.`address`, `Enterprise`.`license_image`, `Enterprise`.`employee_id`, `Enterprise`.`user_id`, `Enterprise`.`remark`, `Enterprise`.`createdAt`, `Enterprise`.`updatedAt`, `employee`.`id` AS `employee.id`, `employee`.`name` AS `employee.name`, `user`.`id` AS `user.id`, `user`.`name` AS `user.name` FROM `enterprise` AS `Enterprise` LEFT OUTER JOIN `employee` AS `employee` ON `Enterprise`.`employee_id` = `employee`.`id` LEFT OUTER JOIN `user` AS `user` ON `Enterprise`.`user_id` = `user`.`id` ORDER BY `Enterprise`.`createdAt` DESC;
1|customer | Executing (default): SELECT `id`, `user_id`, `name`, `nickname`, `mobile`, `email`, `avatar_url`, `wechat_unionid`, `wechat_openid`, `login_type`, `is_partner`, `partner_id`, `commission_ratio`, `remark`, `createdAt`, `updatedAt` FROM `user` AS `User` LIMIT 20;
1|customer | Executing (default): SELECT `Asset`.`id`, `Asset`.`asset_id`, `Asset`.`enterprise_id`, `Asset`.`user_id`, `Asset`.`status`, `Asset`.`product_id`, `Asset`.`user_count`, `Asset`.`account_count`, `Asset`.`duration_months`, `Asset`.`selected_features`, `Asset`.`purchase_date`, `Asset`.`product_expiry_date`, `Asset`.`sps_expiry_date`, `Asset`.`after_sales_expiry_date`, `Asset`.`product_standard_price`, `Asset`.`sps_annual_fee`, `Asset`.`after_sales_service_fee`, `Asset`.`implementation_fee`, `Asset`.`activation_code`, `Asset`.`activation_phone`, `Asset`.`activation_password`, `Asset`.`remark`, `Asset`.`creator_id`, `Asset`.`createdAt`, `Asset`.`updatedAt`, `enterprise`.`id` AS `enterprise.id`, `enterprise`.`enterprise_id` AS `enterprise.enterprise_id`, `enterprise`.`name` AS `enterprise.name`, `enterprise`.`tax_number` AS `enterprise.tax_number`, `enterprise`.`bank_name` AS `enterprise.bank_name`, `enterprise`.`bank_account` AS `enterprise.bank_account`, `enterprise`.`invoice_type` AS `enterprise.invoice_type`, `enterprise`.`contact_person` AS `enterprise.contact_person`, `enterprise`.`contact_phone` AS `enterprise.contact_phone`, `enterprise`.`address` AS `enterprise.address`, `enterprise`.`license_image` AS `enterprise.license_image`, `enterprise`.`employee_id` AS `enterprise.employee_id`, `enterprise`.`user_id` AS `enterprise.user_id`, `enterprise`.`remark` AS `enterprise.remark`, `enterprise`.`createdAt` AS `enterprise.createdAt`, `enterprise`.`updatedAt` AS `enterprise.updatedAt`, `product`.`id` AS `product.id`, `product`.`product_id` AS `product.product_id`, `product`.`product_name` AS `product.product_name`, `product`.`version_name` AS `product.version_name`, `product`.`base_price` AS `product.base_price`, `product`.`base_account_count` AS `product.base_account_count`, `product`.`base_user_count` AS `product.base_user_count`, `product`.`allow_user_addon` AS `product.allow_user_addon`, `product`.`allow_account_addon` AS `product.allow_account_addon`, `product`.`addons` AS `product.addons`, `product`.`remark` AS `product.remark`, `product`.`createdAt` AS `product.createdAt`, `product`.`updatedAt` AS `product.updatedAt`, `user`.`id` AS `user.id`, `user`.`user_id` AS `user.user_id`, `user`.`name` AS `user.name`, `user`.`nickname` AS `user.nickname`, `user`.`mobile` AS `user.mobile`, `user`.`email` AS `user.email`, `user`.`avatar_url` AS `user.avatar_url`, `user`.`wechat_unionid` AS `user.wechat_unionid`, `user`.`wechat_openid` AS `user.wechat_openid`, `user`.`login_type` AS `user.login_type`, `user`.`is_partner` AS `user.is_partner`, `user`.`partner_id` AS `user.partner_id`, `user`.`commission_ratio` AS `user.commission_ratio`, `user`.`remark` AS `user.remark`, `user`.`createdAt` AS `user.createdAt`, `user`.`updatedAt` AS `user.updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`employee_number` AS `creator.employee_number`, `creator`.`name` AS `creator.name`, `creator`.`mobile` AS `creator.mobile`, `creator`.`department` AS `creator.department`, `creator`.`password` AS `creator.password`, `creator`.`role` AS `creator.role`, `creator`.`remark` AS `creator.remark` FROM `asset` AS `Asset` INNER JOIN `enterprise` AS `enterprise` ON `Asset`.`enterprise_id` = `enterprise`.`id` LEFT OUTER JOIN `product` AS `product` ON `Asset`.`product_id` = `product`.`id` LEFT OUTER JOIN `user` AS `user` ON `Asset`.`user_id` = `user`.`id` LEFT OUTER JOIN `employee` AS `creator` ON `Asset`.`creator_id` = `creator`.`id`;
1|customer | Executing (default): SELECT `Enterprise`.`id`, `Enterprise`.`enterprise_id`, `Enterprise`.`name`, `Enterprise`.`tax_number`, `Enterprise`.`bank_name`, `Enterprise`.`bank_account`, `Enterprise`.`invoice_type`, `Enterprise`.`contact_person`, `Enterprise`.`contact_phone`, `Enterprise`.`address`, `Enterprise`.`license_image`, `Enterprise`.`employee_id`, `Enterprise`.`user_id`, `Enterprise`.`remark`, `Enterprise`.`createdAt`, `Enterprise`.`updatedAt`, `employee`.`id` AS `employee.id`, `employee`.`name` AS `employee.name`, `user`.`id` AS `user.id`, `user`.`name` AS `user.name` FROM `enterprise` AS `Enterprise` LEFT OUTER JOIN `employee` AS `employee` ON `Enterprise`.`employee_id` = `employee`.`id` LEFT OUTER JOIN `user` AS `user` ON `Enterprise`.`user_id` = `user`.`id` ORDER BY `Enterprise`.`createdAt` DESC;
1|customer | Executing (default): SELECT `id`, `user_id`, `name`, `nickname`, `mobile`, `email`, `avatar_url`, `wechat_unionid`, `wechat_openid`, `login_type`, `is_partner`, `partner_id`, `commission_ratio`, `remark`, `createdAt`, `updatedAt` FROM `user` AS `User` LIMIT 20;
1|customer | Executing (default): SELECT `id`, `employee_number`, `name`, `mobile`, `department`, `role`, `remark` FROM `employee` AS `Employee`;
1|customer | Executing (default): SELECT `enterprise_id` FROM `enterprise` AS `Enterprise` WHERE `Enterprise`.`enterprise_id` REGEXP '^[0-9]+$' ORDER BY LENGTH(`enterprise_id`) DESC, `Enterprise`.`enterprise_id` DESC LIMIT 1;
1|customer | Executing (default): SELECT `id`, `enterprise_id`, `name`, `tax_number`, `bank_name`, `bank_account`, `invoice_type`, `contact_person`, `contact_phone`, `address`, `license_image`, `employee_id`, `user_id`, `remark`, `createdAt`, `updatedAt` FROM `enterprise` AS `Enterprise` WHERE `Enterprise`.`enterprise_id` = '000005';
1|customer | Executing (default): INSERT INTO `enterprise` (`id`,`enterprise_id`,`name`,`tax_number`,`bank_name`,`bank_account`,`invoice_type`,`contact_person`,`contact_phone`,`address`,`license_image`,`employee_id`,`remark`,`createdAt`,`updatedAt`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?,?,?,?,?);
1|customer | Executing (default): SELECT 1+1 AS result
1|customer | 数据库连接成功，且模型关联已加载。
1|customer | 服务器正在端口 3002 上运行.