require('dotenv').config();
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
// 不再直接引入sequelize，而是引入我们包含所有模型的调度中心
const db = require('./src/models');

// 引入所有路由
const enterpriseRoutes = require('./src/routes/enterprise.routes');
const employeeRoutes = require('./src/routes/employee.routes');
const userRoutes = require('./src/routes/user.routes');
const followupRoutes = require('./src/routes/followup.routes');
// 1. 在这里引入我们新建的产品路由
const productRoutes = require('./src/routes/product.routes');
// 1. 引入产品功能路由
const productFeatureRoutes = require('./src/routes/product_feature.routes.js');
// 1. 引入资产路由
const assetRoutes = require('./src/routes/asset.routes.js');
const orderRoutes = require('./src/routes/order.routes');
const userAuthenticationRoutes = require('./src/routes/user_authentication.routes.js');
const authRoutes = require('./src/routes/auth.routes.js');
const pricingRoutes = require('./src/routes/pricing.routes'); // [新增] 引入价格计算路由
// [修正] 引入阶梯价格路由，并使用更具描述性的变量名
const productUserAddonTierRoutes = require('./src/routes/product_user_addon_tier.routes.js');

const app = express();

// Middlewares
// CORS配置 - 允许你的域名访问
const corsOptions = {
  origin: [
    'https://admin.bogoo.net',    // 管理后台域名
    'https://service.bogoo.net'   // API服务域名
  ],
  methods: "GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS",
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  credentials: true,
  optionsSuccessStatus: 200 // 支持旧版浏览器
};
app.use(cors(corsOptions));

app.use(express.json()); // 解析JSON格式的请求体
app.use(express.urlencoded({ extended: true })); // 解析application/x-www-form-urlencoded格式的请求体

// 让 'uploads' 目录成为可公开访问的静态资源目录
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 基本路由，用于测试服务是否启动
app.get('/', (req, res) => {
  res.send('后端服务已成功启动！');
});

// 使用路由
app.use('/api/enterprises', enterpriseRoutes);
app.use('/api/employees', employeeRoutes);
app.use('/api/users', userRoutes);
app.use('/api/followups', followupRoutes);
// 2. 在这里使用产品路由，并指定基础路径 /api/products
app.use('/api/products', productRoutes);
// 2. 使用产品功能路由
app.use('/api/features', productFeatureRoutes);
// 2. 使用资产路由
app.use('/api/assets', assetRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/user-authentications', userAuthenticationRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/pricing', pricingRoutes); // [新增] 应用价格计算路由
// [修正] 应用阶梯价格路由
app.use('/api', productUserAddonTierRoutes);

/**
 * 定义一个主函数来启动整个应用
 * 这是一个行业标准做法，可以更好地处理异步操作
 */
const startApp = async () => {
  try {
    // 使用模型调度中心的 sequelize 实例进行认证
    await db.sequelize.authenticate();
    console.log('数据库连接成功，且模型关联已加载。');

    // 第二步：数据库连接成功后，才启动 Express 服务
    const PORT = process.env.PORT || 3002;

    // 直接启动服务器
    app.listen(PORT, () => {
      console.log(`服务器正在端口 ${PORT} 上运行.`);
    });

  } catch (error) {
    // 如果数据库连接失败，打印错误并退出程序
    console.error('启动服务失败: 无法连接到数据库。', error);
    process.exit(1); // 退出程序，1表示异常退出
  }
};

// 调用主函数来启动应用
startApp();

// 全局错误处理中间件
app.use((err, req, res, next) => {
  // ... existing code ...
});
