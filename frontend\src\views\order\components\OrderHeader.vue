<template>
  <div class="order-header">
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="120px">
      <el-row :gutter="20">
        <!-- 第一行：订单基本信息 -->
        <el-col :span="8">
          <el-form-item label="订单号" prop="order_id">
            <el-input 
              v-model="formData.order_id" 
              :disabled="!isEditing || mode === 'edit'"
              placeholder="自动生成"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="订单大类" prop="order_category">
            <el-select
              v-model="formData.order_category"
              disabled
              style="width: 100%"
            >
              <el-option label="产品订单" value="产品订单" />
              <el-option label="服务订单" value="服务订单" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建方式" prop="creation_method">
            <el-select 
              v-model="formData.creation_method" 
              :disabled="!isEditing"
              style="width: 100%"
            >
              <el-option label="手工创建" value="手工创建" />
              <el-option label="用户创建" value="用户创建" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 第二行：关联信息 -->
        <el-col :span="8">
          <el-form-item label="企业" prop="enterprise_id">
            <el-select
              v-model="formData.enterprise_id"
              :disabled="!isEditing"
              style="width: 100%"
              filterable
              remote
              :remote-method="searchEnterprises"
              :loading="enterpriseLoading"
              placeholder="选择企业"
              @change="handleEnterpriseChange"
              @focus="initializeEntityData"
              clearable
            >
              <el-option
                v-for="enterprise in enterprises"
                :key="enterprise.id"
                :label="`${enterprise.name} (${enterprise.enterprise_id})`"
                :value="enterprise.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="资产" prop="asset_id">
            <el-select 
              v-model="formData.asset_id" 
              :disabled="!isEditing"
              style="width: 100%"
              filterable
              placeholder="选择资产"
              @change="handleAssetChange"
              clearable
            >
              <el-option
                v-for="asset in availableAssets"
                :key="asset.id"
                :label="`${asset.asset_id} - ${asset.product?.product_name || 'N/A'}`"
                :value="asset.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="用户" prop="user_id">
            <el-select
              v-model="formData.user_id"
              :disabled="!isEditing"
              style="width: 100%"
              filterable
              remote
              :remote-method="searchUsers"
              :loading="userLoading"
              placeholder="选择用户"
              @focus="initializeEntityData"
              @change="handleUserChange"
              clearable
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="`${user.name} (${user.mobile})`"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 第三行：订单类型和金额 -->
        <el-col :span="8">
          <el-form-item label="订单类型" prop="order_type">
            <el-select 
              v-model="formData.order_type" 
              :disabled="!isEditing"
              style="width: 100%"
            >
              <el-option label="普通订单" value="普通订单" />
              <el-option label="续费订单" value="续费订单" />
              <el-option label="变更订单" value="变更订单" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="标准价格" prop="standard_amount">
            <el-input
              v-model.number="formData.standard_amount"
              :disabled="!isEditing"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实付价格" prop="actual_amount">
            <el-input
              v-model.number="formData.actual_amount"
              :disabled="!isEditing"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 产品订单特有字段 -->
      <div v-if="formData.order_category === '产品订单'">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="税额">
              <el-input
                v-model.number="formData.tax_amount"
                :disabled="!isEditing"
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发票类型">
              <el-select 
                v-model="formData.invoice_type" 
                :disabled="!isEditing"
                style="width: 100%"
              >
                <el-option label="不开票" value="不开票" />
                <el-option label="普票" value="普票" />
                <el-option label="专票" value="专票" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="支付状态">
              <el-switch
                v-model="isPaid"
                :disabled="!isEditing"
                active-text="已支付"
                inactive-text="待支付"
                @change="handlePaymentStatusChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 只有已支付时才显示支付方式和支付时间 -->
        <el-row v-if="isPaid" :gutter="20">
          <el-col :span="8">
            <el-form-item label="支付方式">
              <el-select
                v-model="formData.payment_method"
                :disabled="!isEditing"
                style="width: 100%"
              >
                <el-option label="在线" value="在线" />
                <el-option label="对公" value="对公" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="支付时间">
              <el-date-picker
                v-model="formData.payment_time"
                :disabled="!isEditing"
                type="datetime"
                placeholder="选择支付时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 合伙人信息 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="合伙人订单">
            <el-switch 
              v-model="formData.is_partner_order" 
              :disabled="!isEditing"
              @change="handlePartnerOrderChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="formData.is_partner_order">
          <el-form-item label="合伙人ID">
            <el-input 
              v-model="formData.partner_id" 
              :disabled="!isEditing"
              placeholder="输入合伙人ID"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="formData.is_partner_order">
          <el-form-item label="分润比例">
            <el-input-number 
              v-model="formData.commission_ratio" 
              :disabled="!isEditing"
              :precision="4"
              :min="0"
              :max="1"
              :step="0.01"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="formData.is_partner_order">
        <el-col :span="8">
          <el-form-item label="分润金额">
            <el-input-number 
              v-model="formData.commission_amount" 
              :disabled="!isEditing"
              :precision="2"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分润状态">
            <el-select 
              v-model="formData.commission_status" 
              :disabled="!isEditing"
              style="width: 100%"
            >
              <el-option label="已发放" value="已发放" />
              <el-option label="未发放" value="未发放" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>


    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { getNextOrderId } from '@/api/order';
import { useEntityLinkage } from '@/composables/useEntityLinkage';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'create' // create, edit, view, review
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'validate']);

// 响应式数据
const formRef = ref(null);
const formData = ref({
  order_id: '',
  order_category: '产品订单',
  enterprise_id: null,
  asset_id: null,
  user_id: null,
  creation_method: '手工创建',
  order_type: '普通订单',
  standard_amount: 0,
  actual_amount: 0,
  tax_amount: null,
  invoice_type: '不开票',
  payment_status: '待支付',
  payment_method: '在线',
  payment_time: null,
  is_partner_order: false,
  partner_id: null,
  commission_ratio: null,
  commission_amount: null,
  commission_status: '未发放',
  remark: ''
});

// 使用企业、用户、资产联动逻辑
const {
  enterprises,
  users,
  assets: availableAssets,
  enterpriseLoading,
  userLoading,
  assetLoading,
  selectedEnterprise,
  selectedUser,
  selectedAsset,
  isValid,
  searchEnterprises,
  searchUsers,
  handleEnterpriseChange: onEnterpriseChange,
  handleUserChange: onUserChange,
  handleAssetChange: onAssetChange,
  initialize: initializeEntityData
} = useEntityLinkage();

// 支付状态开关
const isPaid = ref(false);

// 表单验证规则
const rules = {
  order_category: [
    { required: true, message: '请选择订单大类', trigger: 'change' }
  ],
  creation_method: [
    { required: true, message: '请选择创建方式', trigger: 'change' }
  ],
  order_type: [
    { required: true, message: '请选择订单类型', trigger: 'change' }
  ],
  enterprise_id: [
    {
      validator: (rule, value, callback) => {
        // 企业ID和用户ID不能同时为空
        const enterpriseId = value || formData.value.enterprise_id;
        const userId = formData.value.user_id;

        console.log('企业字段验证:', {
          value,
          enterpriseId,
          userId,
          enterpriseType: typeof enterpriseId,
          userType: typeof userId
        });

        if (!enterpriseId && !userId) {
          callback(new Error('企业和用户不能同时为空，请至少选择一个'));
        } else {
          callback();
        }
      },
      trigger: ['change', 'blur']
    }
  ],
  user_id: [
    {
      validator: (rule, value, callback) => {
        // 企业ID和用户ID不能同时为空
        const userId = value || formData.value.user_id;
        const enterpriseId = formData.value.enterprise_id;

        console.log('用户字段验证:', {
          value,
          userId,
          enterpriseId,
          userType: typeof userId,
          enterpriseType: typeof enterpriseId
        });

        if (!userId && !enterpriseId) {
          callback(new Error('企业和用户不能同时为空，请至少选择一个'));
        } else {
          callback();
        }
      },
      trigger: ['change', 'blur']
    }
  ],
  standard_amount: [
    { required: true, message: '请输入标准金额', trigger: 'blur' }
  ],
  actual_amount: [
    { required: true, message: '请输入实付金额', trigger: 'blur' }
  ]
};

// 标志变量，避免在props更新时触发循环
let isUpdatingFromProps = false;

// 简化的数据同步机制
watch(() => props.modelValue, (newValue) => {
  if (newValue && typeof newValue === 'object') {
    isUpdatingFromProps = true;

    console.log('Props更新，接收到的数据:', newValue);
    console.log('当前formData:', formData.value);

    // 合并数据，但不用null值覆盖已有的正确值
    const mergedData = {
      ...formData.value, // 保留默认值
      ...newValue,       // 覆盖传入的值
      // 确保关键字段有默认值
      creation_method: newValue.creation_method || '手工创建',
      order_type: newValue.order_type || '普通订单',
      payment_method: newValue.payment_method || '在线',
      invoice_type: newValue.invoice_type || '不开票',
      payment_status: newValue.payment_status || '待支付',
      commission_status: newValue.commission_status || '未发放',
      audit_status: newValue.audit_status || '待审核'
    };

    // 特殊处理：如果传入的enterprise_id或user_id是null，但当前formData中有值，则保留当前值
    if (newValue.enterprise_id === null && formData.value.enterprise_id !== null) {
      mergedData.enterprise_id = formData.value.enterprise_id;
      console.log('保留当前enterprise_id:', formData.value.enterprise_id);
    }
    if (newValue.user_id === null && formData.value.user_id !== null) {
      mergedData.user_id = formData.value.user_id;
      console.log('保留当前user_id:', formData.value.user_id);
    }

    formData.value = mergedData;

    console.log('合并后的formData:', formData.value);

    // 使用nextTick确保DOM更新后再重置标志
    nextTick(() => {
      isUpdatingFromProps = false;
    });
  }
}, { immediate: true });

// 监听 composable 中的选择变化，同步到表单数据
watch([selectedEnterprise, selectedUser, selectedAsset], ([enterpriseId, userId, assetId]) => {
  console.log('监听到 composable 选择变化:', {
    enterpriseId,
    userId,
    assetId,
    currentFormData: {
      enterprise_id: formData.value.enterprise_id,
      user_id: formData.value.user_id,
      asset_id: formData.value.asset_id
    }
  });

  if (enterpriseId !== formData.value.enterprise_id) {
    formData.value.enterprise_id = enterpriseId;
    console.log('更新 formData.enterprise_id 为:', enterpriseId);
  }
  if (userId !== formData.value.user_id) {
    formData.value.user_id = userId;
    console.log('更新 formData.user_id 为:', userId);
  }
  if (assetId !== formData.value.asset_id) {
    formData.value.asset_id = assetId;
    console.log('更新 formData.asset_id 为:', assetId);
  }
});

// 监听表单数据变化，同步到 composable
watch(() => formData.value.enterprise_id, (newVal) => {
  if (newVal !== selectedEnterprise.value) {
    selectedEnterprise.value = newVal;
  }
});

watch(() => formData.value.user_id, (newVal) => {
  if (newVal !== selectedUser.value) {
    selectedUser.value = newVal;
  }
});

watch(() => formData.value.asset_id, (newVal) => {
  if (newVal !== selectedAsset.value) {
    selectedAsset.value = newVal;
  }
});

// 监听支付状态变化，同步开关状态
watch(() => formData.value.payment_status, (newVal) => {
  isPaid.value = newVal === '已支付';
}, { immediate: true });

// 简化的更新函数
const updateParent = () => {
  console.log('OrderHeader updateParent 被调用，当前数据:', {
    enterprise_id: formData.value.enterprise_id,
    user_id: formData.value.user_id,
    formData: formData.value
  });
  emit('update:modelValue', { ...formData.value });
};

// 监听formData变化，自动同步到父组件（避免在props更新时触发）
// 使用节流来避免频繁更新，并且只在非价格字段变化时立即更新
let updateTimer = null;
watch(() => formData.value, (newValue, oldValue) => {
  if (!isUpdatingFromProps) {
    // 清除之前的定时器
    if (updateTimer) {
      clearTimeout(updateTimer);
    }

    // 检查是否只是价格字段变化
    const isPriceOnlyChange = oldValue && (
      newValue.standard_amount !== oldValue.standard_amount ||
      newValue.actual_amount !== oldValue.actual_amount
    );

    if (isPriceOnlyChange) {
      // 价格字段变化时，延迟更新以避免干扰表体数据
      updateTimer = setTimeout(() => {
        updateParent();
      }, 300);
    } else {
      // 其他字段变化时立即更新
      updateParent();
    }
  }
}, { deep: true });

// 注意：在分离架构中，订单类型是固定的，不需要处理变化

// 企业、用户、资产的搜索和加载逻辑已移至 useEntityLinkage composable

// 资产加载逻辑已移至 useEntityLinkage composable

// 生成订单号的函数
const generateOrderId = async (orderType) => {
  if (props.mode === 'create' && props.isEditing) {
    try {
      const response = await getNextOrderId(orderType);
      formData.value.order_id = response.next_id;
    } catch (error) {
      console.error('获取订单号失败:', error);
      ElMessage.error('获取订单号失败');
    }
  }
};



// 处理企业变化
const handleEnterpriseChange = async (enterpriseId) => {
  // 确保数据类型正确（转换为数字或null）
  const cleanEnterpriseId = enterpriseId ? parseInt(enterpriseId) : null;

  // 更新表单数据
  formData.value.enterprise_id = cleanEnterpriseId;

  console.log('企业变化处理:', {
    原始值: enterpriseId,
    清理后: cleanEnterpriseId,
    类型: typeof cleanEnterpriseId
  });

  // 调用 composable 的处理函数
  await onEnterpriseChange(cleanEnterpriseId);

  // 同步 composable 的选择到表单数据
  if (!cleanEnterpriseId) {
    formData.value.asset_id = null;
  }

  // 触发用户字段验证
  formRef.value?.validateField('user_id');
  // 也触发企业字段验证
  formRef.value?.validateField('enterprise_id');

  // 立即同步数据到父组件
  updateParent();
};

// 处理资产变化
const handleAssetChange = async (assetId) => {
  // 更新表单数据
  formData.value.asset_id = assetId;

  // 调用 composable 的处理函数
  await onAssetChange(assetId);

  // 立即同步数据到父组件
  updateParent();
};

// 处理用户变化
const handleUserChange = async (userId) => {
  // 确保数据类型正确（转换为数字或null）
  const cleanUserId = userId ? parseInt(userId) : null;

  // 更新表单数据
  formData.value.user_id = cleanUserId;

  console.log('用户变化处理:', {
    原始值: userId,
    清理后: cleanUserId,
    类型: typeof cleanUserId
  });

  // 调用 composable 的处理函数
  await onUserChange(cleanUserId);

  // 触发企业字段验证
  formRef.value?.validateField('enterprise_id');
  // 也触发用户字段验证
  formRef.value?.validateField('user_id');

  // 立即同步数据到父组件
  updateParent();
};

// 联动逻辑已移至 useEntityLinkage composable

// 处理支付状态变化
const handlePaymentStatusChange = (value) => {
  formData.value.payment_status = value ? '已支付' : '待支付';

  // 如果改为未支付，清空支付方式和支付时间
  if (!value) {
    formData.value.payment_method = '在线';
    formData.value.payment_time = null;
  } else {
    // 如果改为已支付且没有支付时间，设置当前时间
    if (!formData.value.payment_time) {
      formData.value.payment_time = new Date().toISOString().slice(0, 19).replace('T', ' ');
    }
  }
};

// 处理合伙人订单变化
const handlePartnerOrderChange = (value) => {
  if (!value) {
    // 关闭合伙人订单时，清空相关字段
    formData.value.partner_id = null;
    formData.value.commission_ratio = null;
    formData.value.commission_amount = null;
    formData.value.commission_status = '未发放';
  }
};

// 表单验证
const validate = async () => {
  try {
    // 添加调试信息
    console.log('OrderHeader 验证前的数据:', {
      enterprise_id: formData.value.enterprise_id,
      user_id: formData.value.user_id,
      enterprise_id_type: typeof formData.value.enterprise_id,
      user_id_type: typeof formData.value.user_id,
      selectedEnterprise: selectedEnterprise.value,
      selectedUser: selectedUser.value
    });

    await formRef.value.validate();
    emit('validate', true);
    return true;
  } catch (error) {
    console.error('OrderHeader 验证失败:', error);
    emit('validate', false);
    return false;
  }
};

// 暴露验证方法给父组件
defineExpose({
  validate
});

// 组件挂载时初始化
onMounted(async () => {
  // 如果是创建模式，自动生成订单号
  if (!formData.value.order_id) {
    const orderType = formData.value.order_category === '产品订单' ? 'product' : 'service';
    await generateOrderId(orderType);
  }

  // 初始化企业、用户、资产数据
  await initializeEntityData();
});
</script>

<style scoped>
.order-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.order-header .el-form-item {
  margin-bottom: 18px;
}

.order-header .el-input-number {
  width: 100%;
}
</style>
