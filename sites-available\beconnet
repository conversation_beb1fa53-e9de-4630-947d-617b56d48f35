# 主域名配置（官网）
server {
    listen 443 ssl;
    server_name bogoo.net;
    ssl_certificate /etc/letsencrypt/live/bogoo.net/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/bogoo.net/privkey.pem; # managed by Certbot

    root /var/www/BeconNetCode;
    index pages/home.html index.html;

    # 添加默认首页重定向
    location = / {
        return 301 /pages/home.html;
    }

    # 静态文件配置
    location /css/ {
        alias /var/www/BeconNetCode/css/;
    }

    location /js/ {
        alias /var/www/BeconNetCode/js/;
    }

    location /image/ {
        alias /var/www/BeconNetCode/image/;
    }

    location /pages/ {
        alias /var/www/BeconNetCode/pages/;
        index home.html;
        try_files $uri $uri/ =404;
    }

    # 添加错误页面配置
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

}

# 子域名配置（小程序API和管理后台）
server {
    listen 443 ssl;
    server_name api.bogoo.net;
    ssl_certificate /etc/letsencrypt/live/bogoo.net/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/bogoo.net/privkey.pem; # managed by Certbot

    # CORS预检请求处理
    add_header Access-Control-Allow-Origin '*';
    add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    
    # 处理OPTIONS请求
    if ($request_method = 'OPTIONS') {
        return 204;
    }

    # API路径处理 - 明确区分API请求
    location /api/ {
        proxy_pass http://127.0.0.1:3003;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 静态资源请求处理
    location /static/ {
        proxy_pass http://127.0.0.1:3003/static/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 管理后台请求处理 - 根路径使用开发服务器
    location / {
        # 开发服务器代理
        proxy_pass http://127.0.0.1:8089;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # WebSocket支持
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

}

# HTTP 重定向到 HTTPS
server {
    if ($host = api.bogoo.net) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    if ($host = bogoo.net) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    listen 80;
    server_name bogoo.net api.bogoo.net;
    return 301 https://$host$request_uri;




}