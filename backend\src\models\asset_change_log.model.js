const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * @description 定义 AssetChangeLog (资产变更记录) 模型/表
 * [重要] 此模型定义已根据最新的 customer_management.sql 文件进行了完全同步。
 */
const AssetChangeLog = sequelize.define(
  'AssetChangeLog',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '自增主键',
    },
    asset_change_id: {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: '资产变更业务ID（可手工修改）',
    },
    change_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: '变更日期（手填）',
    },
    asset_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '关联asset.id',
    },
    snapshot_before: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: '变更前快照',
    },
    snapshot_after: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: '变更后快照',
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注',
    },
    creator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '制单人ID（关联employee.id）',
    },
  },
  {
    tableName: 'asset_change_log',
    // 启用 Sequelize 的自动时间戳，它会自动管理 createdAt 和 updatedAt 字段
    timestamps: true,
    charset: 'utf8mb4',
    collate: 'utf8mb4_0900_ai_ci',
  }
);

module.exports = AssetChangeLog; 