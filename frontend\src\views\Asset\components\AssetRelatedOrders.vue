<template>
  <div class="asset-related-orders">
    <!-- 关联订单组件 - 处理关联订单列表，支持关联多个订单和联查功能 -->
    
    <!-- 新增资产时的关联订单按钮 -->
    <div v-if="mode === 'create'" class="create-mode">
      <div class="form-section">
        <h4 class="section-title">关联订单</h4>
        <div class="order-link-container">
          <el-button 
            type="primary" 
            @click="showOrderSelector = true"
            :disabled="readonly || !enterpriseId"
          >
            <el-icon><Link /></el-icon>
            关联订单
          </el-button>
          
          <div v-if="!enterpriseId" class="tip-text">
            请先选择企业后再关联订单
          </div>
          
          <!-- 已关联的订单显示 -->
          <div v-if="selectedOrders.length > 0" class="selected-orders">
            <el-tag
              v-for="order in selectedOrders"
              :key="order.id"
              closable
              @close="removeOrder(order.id)"
              :disable-transitions="false"
              class="order-tag"
              type="success"
            >
              {{ order.order_id }} - {{ order.order_type }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 查看模式时的关联订单列表 -->
    <div v-else class="view-mode">
      <div class="form-section">
        <h4 class="section-title">关联订单</h4>
        
        <!-- 订单列表表格 -->
        <el-table 
          :data="relatedOrders" 
          v-loading="loading"
          border 
          style="width: 100%"
          empty-text="暂无关联订单"
        >
          <el-table-column prop="order_id" label="订单ID" width="180">
            <template #default="{ row }">
              <el-button 
                link 
                type="primary" 
                @click="viewOrderDetail(row.id)"
              >
                {{ row.order_id }}
              </el-button>
            </template>
          </el-table-column>
          
          <el-table-column prop="order_type" label="订单类型" width="120" />

          <el-table-column prop="actual_amount" label="订单金额" width="120" align="right">
            <template #default="{ row }">
              <span class="amount-text">¥{{ formatAmount(row.actual_amount) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="payment_status" label="支付状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getPaymentStatusType(row.payment_status)">
                {{ row.payment_status }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="createdAt" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.createdAt) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="remark" label="备注" show-overflow-tooltip />
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button 
                size="small" 
                @click="viewOrderDetail(row.id)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 订单选择对话框 -->
    <el-dialog
      v-model="showOrderSelector"
      title="选择关联订单"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="order-selector">
        <!-- 搜索过滤 -->
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索订单ID或备注"
            clearable
            style="width: 300px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <!-- 可选订单列表 -->
        <el-table 
          :data="filteredAvailableOrders" 
          v-loading="loadingOrders"
          border 
          style="width: 100%; margin-top: 16px"
          empty-text="该企业下暂无可关联的订单"
          @selection-change="handleOrderSelection"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="order_id" label="订单ID" width="180" />
          
          <el-table-column prop="order_type" label="订单类型" width="120" />
          
          <el-table-column prop="actual_amount" label="订单金额" width="120" align="right">
            <template #default="{ row }">
              <span>¥{{ formatAmount(row.actual_amount) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="payment_status" label="支付状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getPaymentStatusType(row.payment_status)" size="small">
                {{ row.payment_status }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="createdAt" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.createdAt) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <el-button @click="showOrderSelector = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmOrderSelection"
          :disabled="tempSelectedOrders.length === 0"
        >
          确定关联 ({{ tempSelectedOrders.length }})
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Link, Search } from '@element-plus/icons-vue'
import { getOrdersByEnterprise } from '@/api/order.js'
import { formatDateTime } from '@/utils/format.js'

// Props定义
const props = defineProps({
  // 模式：create-新增资产时关联订单，view-查看已关联订单
  mode: {
    type: String,
    default: 'view',
    validator: (value) => ['create', 'view'].includes(value)
  },
  // 是否只读模式
  readonly: {
    type: Boolean,
    default: false
  },
  // 企业ID（用于过滤订单）
  enterpriseId: {
    type: [Number, String],
    default: null
  },
  // 已关联的订单列表（查看模式时使用）
  relatedOrders: {
    type: Array,
    default: () => []
  },
  // 已选择的订单ID列表（新增模式时使用）
  selectedOrderIds: {
    type: Array,
    default: () => []
  }
})

// 事件定义
const emit = defineEmits(['orders-change'])

const router = useRouter()

// 状态数据
const loading = ref(false)
const loadingOrders = ref(false)
const showOrderSelector = ref(false)
const searchKeyword = ref('')
const availableOrders = ref([])
const tempSelectedOrders = ref([])

// 计算属性：已选择的订单对象列表
const selectedOrders = computed(() => {
  if (props.mode === 'create') {
    return availableOrders.value.filter(order => 
      props.selectedOrderIds.includes(order.id)
    )
  }
  return []
})

// 计算属性：过滤后的可选订单
const filteredAvailableOrders = computed(() => {
  if (!searchKeyword.value) return availableOrders.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return availableOrders.value.filter(order => 
    order.order_id.toLowerCase().includes(keyword) ||
    (order.remark && order.remark.toLowerCase().includes(keyword))
  )
})

// 加载可关联的订单列表
const loadAvailableOrders = async () => {
  if (!props.enterpriseId) return
  
  loadingOrders.value = true
  try {
    // 获取该企业下未绑定资产的订单
    const orders = await getOrdersByEnterprise(props.enterpriseId, { 
      unbound: true // 只获取未绑定资产的订单
    })
    availableOrders.value = orders || []
  } catch (error) {
    console.error('加载可关联订单失败:', error)
    ElMessage.error('加载可关联订单失败')
  } finally {
    loadingOrders.value = false
  }
}

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toFixed(2)
}

// 获取订单状态类型
const getOrderStatusType = (status) => {
  const statusMap = {
    '待确认': 'warning',
    '已确认': 'success',
    '已完成': 'success',
    '已取消': 'danger',
    '已关闭': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态类型
const getPaymentStatusType = (status) => {
  const statusMap = {
    '待支付': 'warning',
    '已支付': 'success'
  }
  return statusMap[status] || 'info'
}

// 查看订单详情
const viewOrderDetail = (orderId) => {
  router.push({ name: 'order-detail', params: { id: orderId } })
}

// 移除订单关联
const removeOrder = (orderId) => {
  const newOrderIds = props.selectedOrderIds.filter(id => id !== orderId)
  emit('orders-change', newOrderIds)
}

// 处理订单选择
const handleOrderSelection = (selection) => {
  tempSelectedOrders.value = selection
}

// 确认订单选择
const confirmOrderSelection = () => {
  const newOrderIds = tempSelectedOrders.value.map(order => order.id)
  const allOrderIds = [...props.selectedOrderIds, ...newOrderIds]
  
  emit('orders-change', allOrderIds)
  showOrderSelector.value = false
  tempSelectedOrders.value = []
  searchKeyword.value = ''
  
  ElMessage.success(`成功关联 ${newOrderIds.length} 个订单`)
}

// 监听企业ID变化
watch(() => props.enterpriseId, (newEnterpriseId) => {
  if (newEnterpriseId && props.mode === 'create') {
    loadAvailableOrders()
  }
}, { immediate: true })

// 监听显示订单选择器
watch(showOrderSelector, (show) => {
  if (show && props.mode === 'create') {
    loadAvailableOrders()
  }
})

// 生命周期
onMounted(() => {
  if (props.mode === 'create' && props.enterpriseId) {
    loadAvailableOrders()
  }
})
</script>

<style scoped>
.asset-related-orders {
  padding: 20px;
}

.form-section {
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
}

.section-title {
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.order-link-container {
  text-align: center;
  padding: 20px;
}

.tip-text {
  margin-top: 10px;
  color: #909399;
  font-size: 14px;
}

.selected-orders {
  margin-top: 20px;
}

.order-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.amount-text {
  font-weight: 600;
  color: #f56c6c;
}

.search-bar {
  margin-bottom: 16px;
}

.order-selector {
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
  }
  
  .search-bar .el-input {
    width: 100% !important;
  }
}
</style>
