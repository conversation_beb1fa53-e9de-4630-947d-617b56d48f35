server {
    listen 443 ssl;
    server_name mshop.bogoo.net;
    
    ssl_certificate /etc/letsencrypt/live/mshop.bogoo.net/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/mshop.bogoo.net/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # 安全头部增强
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # 图片存储目录
    root /var/www/miniprogram_images;
    
    location / {
        # 禁用目录列表
        autoindex off;
        
        # 缓存设置（30天）
        expires 30d;
        add_header Cache-Control "public";
        
        # 文件存在性检查
        try_files $uri =404;
        
        # 安全设置：禁止执行脚本
        location ~ \.(php|py|sh)$ {
            deny all;
            return 403;
        }
        
        # 禁止访问隐藏文件
        location ~ /\. {
            deny all;
            return 403;
        }
    }
    
    # 错误日志
    access_log /var/log/nginx/miniprogram_images.access.log;
    error_log /var/log/nginx/miniprogram_images.error.log;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name mshop.bogoo.net;
    return 301 https://$host$request_uri;
}