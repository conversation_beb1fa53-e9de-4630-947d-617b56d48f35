const { Sequelize } = require('sequelize');

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: 'mysql',
    timezone: '+08:00', // 设置时区为东八区
    // 连接池配置 - 防止内存泄漏
    pool: {
      max: 10,          // 最大连接数
      min: 0,           // 最小连接数
      acquire: 30000,   // 获取连接的最大时间(毫秒)
      idle: 10000,      // 连接空闲的最大时间(毫秒)
      evict: 1000,      // 检查空闲连接的间隔时间(毫秒)
    },
    // 查询配置
    query: {
      timeout: 60000,   // 查询超时时间(毫秒)
    },
    // 日志配置
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    // 重试配置
    retry: {
      max: 3,           // 最大重试次数
    },
    // 其他配置
    dialectOptions: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_0900_ai_ci',
      // 连接超时
      connectTimeout: 60000,
      // 获取连接超时
      acquireTimeout: 60000,
      // 空闲超时
      timeout: 60000,
    },
  }
);

module.exports = sequelize;